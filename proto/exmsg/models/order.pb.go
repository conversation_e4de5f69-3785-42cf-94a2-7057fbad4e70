// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: order.proto

package models

import (
	_struct "github.com/golang/protobuf/ptypes/struct"
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message for order_products
type EditedPriceStatus int32

const (
	EditedPriceStatus_NONE     EditedPriceStatus = 0
	EditedPriceStatus_ACCEPTED EditedPriceStatus = 1
	EditedPriceStatus_REJECTED EditedPriceStatus = 2
)

// Enum value maps for EditedPriceStatus.
var (
	EditedPriceStatus_name = map[int32]string{
		0: "NONE",
		1: "ACCEPTED",
		2: "REJECTED",
	}
	EditedPriceStatus_value = map[string]int32{
		"NONE":     0,
		"ACCEPTED": 1,
		"REJECTED": 2,
	}
)

func (x EditedPriceStatus) Enum() *EditedPriceStatus {
	p := new(EditedPriceStatus)
	*p = x
	return p
}

func (x EditedPriceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EditedPriceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_order_proto_enumTypes[0].Descriptor()
}

func (EditedPriceStatus) Type() protoreflect.EnumType {
	return &file_order_proto_enumTypes[0]
}

func (x EditedPriceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EditedPriceStatus.Descriptor instead.
func (EditedPriceStatus) EnumDescriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{0}
}

// Order represents an order in the system
type Order struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Base fields
	Id        int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamp.Timestamp `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Related IDs
	SaleId           int64 `protobuf:"varint,4,opt,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	SourceId         int64 `protobuf:"varint,5,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	CreatorId        int64 `protobuf:"varint,6,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CarePageId       int64 `protobuf:"varint,7,opt,name=care_page_id,json=carePageId,proto3" json:"care_page_id,omitempty"`
	CountryId        int64 `protobuf:"varint,8,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	ProjectId        int64 `protobuf:"varint,9,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	LastUpdatedBy    int64 `protobuf:"varint,10,opt,name=last_updated_by,json=lastUpdatedBy,proto3" json:"last_updated_by,omitempty"`
	MarketerId       int64 `protobuf:"varint,11,opt,name=marketer_id,json=marketerId,proto3" json:"marketer_id,omitempty"`
	CompanyId        int64 `protobuf:"varint,12,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	ChannelId        int64 `protobuf:"varint,13,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ExternalSourceId int64 `protobuf:"varint,14,opt,name=external_source_id,json=externalSourceId,proto3" json:"external_source_id,omitempty"`
	// Financial fields
	Discount     float64 `protobuf:"fixed64,15,opt,name=discount,proto3" json:"discount,omitempty"`
	Surcharge    float64 `protobuf:"fixed64,16,opt,name=surcharge,proto3" json:"surcharge,omitempty"`
	ShippingFee  float64 `protobuf:"fixed64,17,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee,omitempty"`
	Paid         float64 `protobuf:"fixed64,18,opt,name=paid,proto3" json:"paid,omitempty"`
	TotalPrice   float64 `protobuf:"fixed64,19,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	DiscountType float64 `protobuf:"fixed64,20,opt,name=discount_type,json=discountType,proto3" json:"discount_type,omitempty"`
	// Customer information
	CustomerName  string `protobuf:"bytes,21,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	CustomerPhone string `protobuf:"bytes,22,opt,name=customer_phone,json=customerPhone,proto3" json:"customer_phone,omitempty"`
	// Address fields
	AddressText       string `protobuf:"bytes,23,opt,name=address_text,json=addressText,proto3" json:"address_text,omitempty"`
	AddressNote       string `protobuf:"bytes,24,opt,name=address_note,json=addressNote,proto3" json:"address_note,omitempty"`
	AddressWard       string `protobuf:"bytes,25,opt,name=address_ward,json=addressWard,proto3" json:"address_ward,omitempty"`
	AddressDistrict   string `protobuf:"bytes,26,opt,name=address_district,json=addressDistrict,proto3" json:"address_district,omitempty"`
	AddressProvince   string `protobuf:"bytes,27,opt,name=address_province,json=addressProvince,proto3" json:"address_province,omitempty"`
	AddressWardId     string `protobuf:"bytes,28,opt,name=address_ward_id,json=addressWardId,proto3" json:"address_ward_id,omitempty"`
	AddressDistrictId string `protobuf:"bytes,29,opt,name=address_district_id,json=addressDistrictId,proto3" json:"address_district_id,omitempty"`
	AddressProvinceId string `protobuf:"bytes,30,opt,name=address_province_id,json=addressProvinceId,proto3" json:"address_province_id,omitempty"`
	PostCode          string `protobuf:"bytes,31,opt,name=post_code,json=postCode,proto3" json:"post_code,omitempty"`
	// Status and state
	Status               int64                `protobuf:"varint,32,opt,name=status,proto3" json:"status,omitempty"`
	TeamInCharge         int64                `protobuf:"varint,33,opt,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	CancelReasonText     string               `protobuf:"bytes,34,opt,name=cancel_reason_text,json=cancelReasonText,proto3" json:"cancel_reason_text,omitempty"`
	PackingAt            *timestamp.Timestamp `protobuf:"bytes,35,opt,name=packing_at,json=packingAt,proto3" json:"packing_at,omitempty"`
	DisplayId            string               `protobuf:"bytes,36,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	IsCheckedAfterReturn bool                 `protobuf:"varint,37,opt,name=is_checked_after_return,json=isCheckedAfterReturn,proto3" json:"is_checked_after_return,omitempty"`
	// Partner information
	PartnerId        string `protobuf:"bytes,38,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	PartnerCode      string `protobuf:"bytes,39,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	PartnerDisplayId string `protobuf:"bytes,40,opt,name=partner_display_id,json=partnerDisplayId,proto3" json:"partner_display_id,omitempty"`
	// Social media and external IDs
	PostCodeOld           int64                `protobuf:"varint,41,opt,name=post_code_old,json=postCodeOld,proto3" json:"post_code_old,omitempty"`
	PageId                int64                `protobuf:"varint,42,opt,name=page_id,json=pageId,proto3" json:"page_id,omitempty"`
	SourceDetail          *_struct.Struct      `protobuf:"bytes,43,opt,name=source_detail,json=sourceDetail,proto3" json:"source_detail,omitempty"`
	ConfirmedAt           *timestamp.Timestamp `protobuf:"bytes,44,opt,name=confirmed_at,json=confirmedAt,proto3" json:"confirmed_at,omitempty"`
	PancakeConversationId int64                `protobuf:"varint,45,opt,name=pancake_conversation_id,json=pancakeConversationId,proto3" json:"pancake_conversation_id,omitempty"`
	FbGlobalId            int64                `protobuf:"varint,46,opt,name=fb_global_id,json=fbGlobalId,proto3" json:"fb_global_id,omitempty"`
	ExpectDeliveryAt      *timestamp.Timestamp `protobuf:"bytes,47,opt,name=expect_delivery_at,json=expectDeliveryAt,proto3" json:"expect_delivery_at,omitempty"`
	FbReceiptMessageId    string               `protobuf:"bytes,48,opt,name=fb_receipt_message_id,json=fbReceiptMessageId,proto3" json:"fb_receipt_message_id,omitempty"`
	// Additional information
	PrintNoteText          string               `protobuf:"bytes,49,opt,name=print_note_text,json=printNoteText,proto3" json:"print_note_text,omitempty"`
	Note                   string               `protobuf:"bytes,50,opt,name=note,proto3" json:"note,omitempty"`
	LastUpdateStatus       *timestamp.Timestamp `protobuf:"bytes,51,opt,name=last_update_status,json=lastUpdateStatus,proto3" json:"last_update_status,omitempty"`
	FfmDisplayId           string               `protobuf:"bytes,52,opt,name=ffm_display_id,json=ffmDisplayId,proto3" json:"ffm_display_id,omitempty"`
	FfmCompanyId           string               `protobuf:"bytes,53,opt,name=ffm_company_id,json=ffmCompanyId,proto3" json:"ffm_company_id,omitempty"`
	FbScopedUserId         int64                `protobuf:"varint,54,opt,name=fb_scoped_user_id,json=fbScopedUserId,proto3" json:"fb_scoped_user_id,omitempty"`
	FfmPartnerClientId     int64                `protobuf:"varint,55,opt,name=ffm_partner_client_id,json=ffmPartnerClientId,proto3" json:"ffm_partner_client_id,omitempty"`
	IgnoreDuplicateWarning bool                 `protobuf:"varint,56,opt,name=ignore_duplicate_warning,json=ignoreDuplicateWarning,proto3" json:"ignore_duplicate_warning,omitempty"`
	// Status change information
	StatusChangeReason      string `protobuf:"bytes,57,opt,name=status_change_reason,json=statusChangeReason,proto3" json:"status_change_reason,omitempty"`
	StatusChangeDescription string `protobuf:"bytes,58,opt,name=status_change_description,json=statusChangeDescription,proto3" json:"status_change_description,omitempty"`
	StatusChangeActor       string `protobuf:"bytes,59,opt,name=status_change_actor,json=statusChangeActor,proto3" json:"status_change_actor,omitempty"`
	// Sync timestamps
	LastSyncedFfmStatusAt  *timestamp.Timestamp `protobuf:"bytes,60,opt,name=last_synced_ffm_status_at,json=lastSyncedFfmStatusAt,proto3" json:"last_synced_ffm_status_at,omitempty"`
	LastSyncedLeadUserIdAt *timestamp.Timestamp `protobuf:"bytes,61,opt,name=last_synced_lead_user_id_at,json=lastSyncedLeadUserIdAt,proto3" json:"last_synced_lead_user_id_at,omitempty"`
	LastSyncedFfmInfoAt    *timestamp.Timestamp `protobuf:"bytes,62,opt,name=last_synced_ffm_info_at,json=lastSyncedFfmInfoAt,proto3" json:"last_synced_ffm_info_at,omitempty"`
	LastSyncedFfmTagsAt    *timestamp.Timestamp `protobuf:"bytes,63,opt,name=last_synced_ffm_tags_at,json=lastSyncedFfmTagsAt,proto3" json:"last_synced_ffm_tags_at,omitempty"`
	// Additional flags
	CrossCare       bool   `protobuf:"varint,64,opt,name=cross_care,json=crossCare,proto3" json:"cross_care,omitempty"`
	UtmLink         string `protobuf:"bytes,65,opt,name=utm_link,json=utmLink,proto3" json:"utm_link,omitempty"`
	SourceProjectId int64  `protobuf:"varint,66,opt,name=source_project_id,json=sourceProjectId,proto3" json:"source_project_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_order_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{0}
}

func (x *Order) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Order) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetSaleId() int64 {
	if x != nil {
		return x.SaleId
	}
	return 0
}

func (x *Order) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *Order) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *Order) GetCarePageId() int64 {
	if x != nil {
		return x.CarePageId
	}
	return 0
}

func (x *Order) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *Order) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *Order) GetLastUpdatedBy() int64 {
	if x != nil {
		return x.LastUpdatedBy
	}
	return 0
}

func (x *Order) GetMarketerId() int64 {
	if x != nil {
		return x.MarketerId
	}
	return 0
}

func (x *Order) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Order) GetChannelId() int64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *Order) GetExternalSourceId() int64 {
	if x != nil {
		return x.ExternalSourceId
	}
	return 0
}

func (x *Order) GetDiscount() float64 {
	if x != nil {
		return x.Discount
	}
	return 0
}

func (x *Order) GetSurcharge() float64 {
	if x != nil {
		return x.Surcharge
	}
	return 0
}

func (x *Order) GetShippingFee() float64 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *Order) GetPaid() float64 {
	if x != nil {
		return x.Paid
	}
	return 0
}

func (x *Order) GetTotalPrice() float64 {
	if x != nil {
		return x.TotalPrice
	}
	return 0
}

func (x *Order) GetDiscountType() float64 {
	if x != nil {
		return x.DiscountType
	}
	return 0
}

func (x *Order) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *Order) GetCustomerPhone() string {
	if x != nil {
		return x.CustomerPhone
	}
	return ""
}

func (x *Order) GetAddressText() string {
	if x != nil {
		return x.AddressText
	}
	return ""
}

func (x *Order) GetAddressNote() string {
	if x != nil {
		return x.AddressNote
	}
	return ""
}

func (x *Order) GetAddressWard() string {
	if x != nil {
		return x.AddressWard
	}
	return ""
}

func (x *Order) GetAddressDistrict() string {
	if x != nil {
		return x.AddressDistrict
	}
	return ""
}

func (x *Order) GetAddressProvince() string {
	if x != nil {
		return x.AddressProvince
	}
	return ""
}

func (x *Order) GetAddressWardId() string {
	if x != nil {
		return x.AddressWardId
	}
	return ""
}

func (x *Order) GetAddressDistrictId() string {
	if x != nil {
		return x.AddressDistrictId
	}
	return ""
}

func (x *Order) GetAddressProvinceId() string {
	if x != nil {
		return x.AddressProvinceId
	}
	return ""
}

func (x *Order) GetPostCode() string {
	if x != nil {
		return x.PostCode
	}
	return ""
}

func (x *Order) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Order) GetTeamInCharge() int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return 0
}

func (x *Order) GetCancelReasonText() string {
	if x != nil {
		return x.CancelReasonText
	}
	return ""
}

func (x *Order) GetPackingAt() *timestamp.Timestamp {
	if x != nil {
		return x.PackingAt
	}
	return nil
}

func (x *Order) GetDisplayId() string {
	if x != nil {
		return x.DisplayId
	}
	return ""
}

func (x *Order) GetIsCheckedAfterReturn() bool {
	if x != nil {
		return x.IsCheckedAfterReturn
	}
	return false
}

func (x *Order) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *Order) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *Order) GetPartnerDisplayId() string {
	if x != nil {
		return x.PartnerDisplayId
	}
	return ""
}

func (x *Order) GetPostCodeOld() int64 {
	if x != nil {
		return x.PostCodeOld
	}
	return 0
}

func (x *Order) GetPageId() int64 {
	if x != nil {
		return x.PageId
	}
	return 0
}

func (x *Order) GetSourceDetail() *_struct.Struct {
	if x != nil {
		return x.SourceDetail
	}
	return nil
}

func (x *Order) GetConfirmedAt() *timestamp.Timestamp {
	if x != nil {
		return x.ConfirmedAt
	}
	return nil
}

func (x *Order) GetPancakeConversationId() int64 {
	if x != nil {
		return x.PancakeConversationId
	}
	return 0
}

func (x *Order) GetFbGlobalId() int64 {
	if x != nil {
		return x.FbGlobalId
	}
	return 0
}

func (x *Order) GetExpectDeliveryAt() *timestamp.Timestamp {
	if x != nil {
		return x.ExpectDeliveryAt
	}
	return nil
}

func (x *Order) GetFbReceiptMessageId() string {
	if x != nil {
		return x.FbReceiptMessageId
	}
	return ""
}

func (x *Order) GetPrintNoteText() string {
	if x != nil {
		return x.PrintNoteText
	}
	return ""
}

func (x *Order) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *Order) GetLastUpdateStatus() *timestamp.Timestamp {
	if x != nil {
		return x.LastUpdateStatus
	}
	return nil
}

func (x *Order) GetFfmDisplayId() string {
	if x != nil {
		return x.FfmDisplayId
	}
	return ""
}

func (x *Order) GetFfmCompanyId() string {
	if x != nil {
		return x.FfmCompanyId
	}
	return ""
}

func (x *Order) GetFbScopedUserId() int64 {
	if x != nil {
		return x.FbScopedUserId
	}
	return 0
}

func (x *Order) GetFfmPartnerClientId() int64 {
	if x != nil {
		return x.FfmPartnerClientId
	}
	return 0
}

func (x *Order) GetIgnoreDuplicateWarning() bool {
	if x != nil {
		return x.IgnoreDuplicateWarning
	}
	return false
}

func (x *Order) GetStatusChangeReason() string {
	if x != nil {
		return x.StatusChangeReason
	}
	return ""
}

func (x *Order) GetStatusChangeDescription() string {
	if x != nil {
		return x.StatusChangeDescription
	}
	return ""
}

func (x *Order) GetStatusChangeActor() string {
	if x != nil {
		return x.StatusChangeActor
	}
	return ""
}

func (x *Order) GetLastSyncedFfmStatusAt() *timestamp.Timestamp {
	if x != nil {
		return x.LastSyncedFfmStatusAt
	}
	return nil
}

func (x *Order) GetLastSyncedLeadUserIdAt() *timestamp.Timestamp {
	if x != nil {
		return x.LastSyncedLeadUserIdAt
	}
	return nil
}

func (x *Order) GetLastSyncedFfmInfoAt() *timestamp.Timestamp {
	if x != nil {
		return x.LastSyncedFfmInfoAt
	}
	return nil
}

func (x *Order) GetLastSyncedFfmTagsAt() *timestamp.Timestamp {
	if x != nil {
		return x.LastSyncedFfmTagsAt
	}
	return nil
}

func (x *Order) GetCrossCare() bool {
	if x != nil {
		return x.CrossCare
	}
	return false
}

func (x *Order) GetUtmLink() string {
	if x != nil {
		return x.UtmLink
	}
	return ""
}

func (x *Order) GetSourceProjectId() int64 {
	if x != nil {
		return x.SourceProjectId
	}
	return 0
}

type OrderProduct struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                           // Primary key
	OrderId       int64                  `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`                  // Foreign key to orders table
	Quantity      float64                `protobuf:"fixed64,3,opt,name=quantity,proto3" json:"quantity,omitempty"`                              // Quantity of product
	Price         float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`                                    // Original price of the product
	ProductId     int64                  `protobuf:"varint,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`            // Product ID
	ProductDetail *_struct.Struct        `protobuf:"bytes,6,opt,name=product_detail,json=productDetail,proto3" json:"product_detail,omitempty"` // JSONB data as a string
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderProduct) Reset() {
	*x = OrderProduct{}
	mi := &file_order_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderProduct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderProduct) ProtoMessage() {}

func (x *OrderProduct) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderProduct.ProtoReflect.Descriptor instead.
func (*OrderProduct) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{1}
}

func (x *OrderProduct) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderProduct) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderProduct) GetQuantity() float64 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderProduct) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *OrderProduct) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *OrderProduct) GetProductDetail() *_struct.Struct {
	if x != nil {
		return x.ProductDetail
	}
	return nil
}

type LandingPage struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt *timestamp.Timestamp   `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamp.Timestamp   `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Id        string                 `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Name      string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Link      string                 `protobuf:"bytes,5,opt,name=link,proto3" json:"link,omitempty"`
	CountryId int64                  `protobuf:"varint,6,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	ProjectId int64                  `protobuf:"varint,7,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	UserId    int64                  `protobuf:"varint,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// google.protobuf.Timestamp deleted_at = 9;
	CompanyId     int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CreatorId     int64 `protobuf:"varint,11,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	UpdatedBy     int64 `protobuf:"varint,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	Status        int64 `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	ProductId     int64 `protobuf:"varint,14,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LandingPage) Reset() {
	*x = LandingPage{}
	mi := &file_order_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LandingPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingPage) ProtoMessage() {}

func (x *LandingPage) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingPage.ProtoReflect.Descriptor instead.
func (*LandingPage) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{2}
}

func (x *LandingPage) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LandingPage) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LandingPage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LandingPage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LandingPage) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *LandingPage) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *LandingPage) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *LandingPage) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LandingPage) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LandingPage) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *LandingPage) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *LandingPage) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LandingPage) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

var File_order_proto protoreflect.FileDescriptor

var file_order_proto_rawDesc = string([]byte{
	0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9d, 0x16, 0x0a, 0x05, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x61,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x61, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x65, 0x50, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x65,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x77, 0x61, 0x72, 0x64, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x57, 0x61, 0x72, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x57, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x66, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x6c, 0x64, 0x18, 0x29, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x70, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x6c, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x61, 0x6e, 0x63, 0x61, 0x6b, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x2d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x15, 0x70, 0x61, 0x6e, 0x63, 0x61, 0x6b, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x62,
	0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x66, 0x62, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x12,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x61, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x41, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x66, 0x62, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x66, 0x62, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x31, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c,
	0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x66, 0x66, 0x6d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x66, 0x6d, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x66, 0x6d, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66,
	0x66, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x66,
	0x62, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x36, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x62, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x66, 0x66, 0x6d, 0x5f, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x37, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x66, 0x66, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x5f, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x61,
	0x72, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x38, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x57, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x39, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x54, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64,
	0x5f, 0x66, 0x66, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x3c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x46, 0x66, 0x6d, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x74, 0x12, 0x57, 0x0a, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79,
	0x6e, 0x63, 0x65, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x41, 0x74,
	0x12, 0x50, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f,
	0x66, 0x66, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x61, 0x74, 0x18, 0x3e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x6c,
	0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x46, 0x66, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x41, 0x74, 0x12, 0x50, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x64, 0x5f, 0x66, 0x66, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x3f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x13, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x46, 0x66, 0x6d, 0x54, 0x61,
	0x67, 0x73, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x63, 0x61,
	0x72, 0x65, 0x18, 0x40, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x74, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x74, 0x6d, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x42, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x0c, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xa6, 0x03, 0x0a, 0x0b, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x2a, 0x39, 0x0a, 0x11, 0x45, 0x64, 0x69, 0x74, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a,
	0x08, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x42, 0x36, 0x5a, 0x34, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f,
	0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x3b, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_order_proto_rawDescOnce sync.Once
	file_order_proto_rawDescData []byte
)

func file_order_proto_rawDescGZIP() []byte {
	file_order_proto_rawDescOnce.Do(func() {
		file_order_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_order_proto_rawDesc), len(file_order_proto_rawDesc)))
	})
	return file_order_proto_rawDescData
}

var file_order_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_order_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_order_proto_goTypes = []any{
	(EditedPriceStatus)(0),      // 0: exmsg.models.EditedPriceStatus
	(*Order)(nil),               // 1: exmsg.models.Order
	(*OrderProduct)(nil),        // 2: exmsg.models.OrderProduct
	(*LandingPage)(nil),         // 3: exmsg.models.LandingPage
	(*timestamp.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(*_struct.Struct)(nil),      // 5: google.protobuf.Struct
}
var file_order_proto_depIdxs = []int32{
	4,  // 0: exmsg.models.Order.created_at:type_name -> google.protobuf.Timestamp
	4,  // 1: exmsg.models.Order.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 2: exmsg.models.Order.packing_at:type_name -> google.protobuf.Timestamp
	5,  // 3: exmsg.models.Order.source_detail:type_name -> google.protobuf.Struct
	4,  // 4: exmsg.models.Order.confirmed_at:type_name -> google.protobuf.Timestamp
	4,  // 5: exmsg.models.Order.expect_delivery_at:type_name -> google.protobuf.Timestamp
	4,  // 6: exmsg.models.Order.last_update_status:type_name -> google.protobuf.Timestamp
	4,  // 7: exmsg.models.Order.last_synced_ffm_status_at:type_name -> google.protobuf.Timestamp
	4,  // 8: exmsg.models.Order.last_synced_lead_user_id_at:type_name -> google.protobuf.Timestamp
	4,  // 9: exmsg.models.Order.last_synced_ffm_info_at:type_name -> google.protobuf.Timestamp
	4,  // 10: exmsg.models.Order.last_synced_ffm_tags_at:type_name -> google.protobuf.Timestamp
	5,  // 11: exmsg.models.OrderProduct.product_detail:type_name -> google.protobuf.Struct
	4,  // 12: exmsg.models.LandingPage.created_at:type_name -> google.protobuf.Timestamp
	4,  // 13: exmsg.models.LandingPage.updated_at:type_name -> google.protobuf.Timestamp
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_order_proto_init() }
func file_order_proto_init() {
	if File_order_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_order_proto_rawDesc), len(file_order_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_order_proto_goTypes,
		DependencyIndexes: file_order_proto_depIdxs,
		EnumInfos:         file_order_proto_enumTypes,
		MessageInfos:      file_order_proto_msgTypes,
	}.Build()
	File_order_proto = out.File
	file_order_proto_goTypes = nil
	file_order_proto_depIdxs = nil
}
