// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: proto/services/market.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for MarketService service

type MarketService interface {
	CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, opts ...client.CallOption) (*CreateAnalyticAccountResponse, error)
	ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, opts ...client.CallOption) (*ListAnalyticAccountsResponse, error)
	DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, opts ...client.CallOption) (*emptypb.Empty, error)
	ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, opts ...client.CallOption) (*ListAdAccountsResponse, error)
	BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, opts ...client.CallOption) (*BulkUpdateAdAccountsResponse, error)
	BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, opts ...client.CallOption) (*emptypb.Empty, error)
	ListCampaigns(ctx context.Context, in *ListCampaignsRequest, opts ...client.CallOption) (*ListCampaignsResponse, error)
	BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, opts ...client.CallOption) (*BulkUpdateCampaignsResponse, error)
	AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, opts ...client.CallOption) (*AssignCompanyForCampaignsResponse, error)
	BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, opts ...client.CallOption) (*emptypb.Empty, error)
	ListMarketers(ctx context.Context, in *ListMarketersRequest, opts ...client.CallOption) (*ListMarketersResponse, error)
	BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, opts ...client.CallOption) (*BatchInsertAdAccountsResponse, error)
	BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, opts ...client.CallOption) (*BatchInsertCampaignsResponse, error)
	BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, opts ...client.CallOption) (*BatchInsertAdsetsResponse, error)
	BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, opts ...client.CallOption) (*BatchInsertAdsResponse, error)
	BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, opts ...client.CallOption) (*BatchInsertSpendingsResponse, error)
	GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, opts ...client.CallOption) (*GetTokenByAdIDResponse, error)
}

type marketService struct {
	c    client.Client
	name string
}

func NewMarketService(name string, c client.Client) MarketService {
	return &marketService{
		c:    c,
		name: name,
	}
}

func (c *marketService) CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, opts ...client.CallOption) (*CreateAnalyticAccountResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.CreateAnalyticAccount", in)
	out := new(CreateAnalyticAccountResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, opts ...client.CallOption) (*ListAnalyticAccountsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.ListAnalyticAccounts", in)
	out := new(ListAnalyticAccountsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "MarketService.DeleteAnalyticAccount", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, opts ...client.CallOption) (*ListAdAccountsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.ListAdAccounts", in)
	out := new(ListAdAccountsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, opts ...client.CallOption) (*BulkUpdateAdAccountsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BulkUpdateAdAccounts", in)
	out := new(BulkUpdateAdAccountsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "MarketService.BulkDeleteAdAccounts", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) ListCampaigns(ctx context.Context, in *ListCampaignsRequest, opts ...client.CallOption) (*ListCampaignsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.ListCampaigns", in)
	out := new(ListCampaignsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, opts ...client.CallOption) (*BulkUpdateCampaignsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BulkUpdateCampaigns", in)
	out := new(BulkUpdateCampaignsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, opts ...client.CallOption) (*AssignCompanyForCampaignsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.AssignCompanyForCampaigns", in)
	out := new(AssignCompanyForCampaignsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "MarketService.BulkDeleteCampaigns", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) ListMarketers(ctx context.Context, in *ListMarketersRequest, opts ...client.CallOption) (*ListMarketersResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.ListMarketers", in)
	out := new(ListMarketersResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, opts ...client.CallOption) (*BatchInsertAdAccountsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BatchInsertAdAccounts", in)
	out := new(BatchInsertAdAccountsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, opts ...client.CallOption) (*BatchInsertCampaignsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BatchInsertCampaigns", in)
	out := new(BatchInsertCampaignsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, opts ...client.CallOption) (*BatchInsertAdsetsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BatchInsertAdsets", in)
	out := new(BatchInsertAdsetsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, opts ...client.CallOption) (*BatchInsertAdsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BatchInsertAds", in)
	out := new(BatchInsertAdsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, opts ...client.CallOption) (*BatchInsertSpendingsResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.BatchInsertSpendings", in)
	out := new(BatchInsertSpendingsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketService) GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, opts ...client.CallOption) (*GetTokenByAdIDResponse, error) {
	req := c.c.NewRequest(c.name, "MarketService.GetTokenByAdID", in)
	out := new(GetTokenByAdIDResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for MarketService service

type MarketServiceHandler interface {
	CreateAnalyticAccount(context.Context, *CreateAnalyticAccountRequest, *CreateAnalyticAccountResponse) error
	ListAnalyticAccounts(context.Context, *ListAnalyticAccountsRequest, *ListAnalyticAccountsResponse) error
	DeleteAnalyticAccount(context.Context, *DeleteAnalyticAccountRequest, *emptypb.Empty) error
	ListAdAccounts(context.Context, *ListAdAccountsRequest, *ListAdAccountsResponse) error
	BulkUpdateAdAccounts(context.Context, *BulkUpdateAdAccountsRequest, *BulkUpdateAdAccountsResponse) error
	BulkDeleteAdAccounts(context.Context, *BulkDeleteAdAccountsRequest, *emptypb.Empty) error
	ListCampaigns(context.Context, *ListCampaignsRequest, *ListCampaignsResponse) error
	BulkUpdateCampaigns(context.Context, *BulkUpdateCampaignsRequest, *BulkUpdateCampaignsResponse) error
	AssignCompanyForCampaigns(context.Context, *AssignCompanyForCampaignsRequest, *AssignCompanyForCampaignsResponse) error
	BulkDeleteCampaigns(context.Context, *BulkDeleteCampaignsRequest, *emptypb.Empty) error
	ListMarketers(context.Context, *ListMarketersRequest, *ListMarketersResponse) error
	BatchInsertAdAccounts(context.Context, *BatchInsertAdAccountsRequest, *BatchInsertAdAccountsResponse) error
	BatchInsertCampaigns(context.Context, *BatchInsertCampaignsRequest, *BatchInsertCampaignsResponse) error
	BatchInsertAdsets(context.Context, *BatchInsertAdsetsRequest, *BatchInsertAdsetsResponse) error
	BatchInsertAds(context.Context, *BatchInsertAdsRequest, *BatchInsertAdsResponse) error
	BatchInsertSpendings(context.Context, *BatchInsertSpendingsRequest, *BatchInsertSpendingsResponse) error
	GetTokenByAdID(context.Context, *GetTokenByAdIDRequest, *GetTokenByAdIDResponse) error
}

func RegisterMarketServiceHandler(s server.Server, hdlr MarketServiceHandler, opts ...server.HandlerOption) error {
	type marketService interface {
		CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, out *CreateAnalyticAccountResponse) error
		ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, out *ListAnalyticAccountsResponse) error
		DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, out *emptypb.Empty) error
		ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, out *ListAdAccountsResponse) error
		BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, out *BulkUpdateAdAccountsResponse) error
		BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, out *emptypb.Empty) error
		ListCampaigns(ctx context.Context, in *ListCampaignsRequest, out *ListCampaignsResponse) error
		BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, out *BulkUpdateCampaignsResponse) error
		AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, out *AssignCompanyForCampaignsResponse) error
		BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, out *emptypb.Empty) error
		ListMarketers(ctx context.Context, in *ListMarketersRequest, out *ListMarketersResponse) error
		BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, out *BatchInsertAdAccountsResponse) error
		BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, out *BatchInsertCampaignsResponse) error
		BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, out *BatchInsertAdsetsResponse) error
		BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, out *BatchInsertAdsResponse) error
		BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, out *BatchInsertSpendingsResponse) error
		GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, out *GetTokenByAdIDResponse) error
	}
	type MarketService struct {
		marketService
	}
	h := &marketServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&MarketService{h}, opts...))
}

type marketServiceHandler struct {
	MarketServiceHandler
}

func (h *marketServiceHandler) CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, out *CreateAnalyticAccountResponse) error {
	return h.MarketServiceHandler.CreateAnalyticAccount(ctx, in, out)
}

func (h *marketServiceHandler) ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, out *ListAnalyticAccountsResponse) error {
	return h.MarketServiceHandler.ListAnalyticAccounts(ctx, in, out)
}

func (h *marketServiceHandler) DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, out *emptypb.Empty) error {
	return h.MarketServiceHandler.DeleteAnalyticAccount(ctx, in, out)
}

func (h *marketServiceHandler) ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, out *ListAdAccountsResponse) error {
	return h.MarketServiceHandler.ListAdAccounts(ctx, in, out)
}

func (h *marketServiceHandler) BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, out *BulkUpdateAdAccountsResponse) error {
	return h.MarketServiceHandler.BulkUpdateAdAccounts(ctx, in, out)
}

func (h *marketServiceHandler) BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, out *emptypb.Empty) error {
	return h.MarketServiceHandler.BulkDeleteAdAccounts(ctx, in, out)
}

func (h *marketServiceHandler) ListCampaigns(ctx context.Context, in *ListCampaignsRequest, out *ListCampaignsResponse) error {
	return h.MarketServiceHandler.ListCampaigns(ctx, in, out)
}

func (h *marketServiceHandler) BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, out *BulkUpdateCampaignsResponse) error {
	return h.MarketServiceHandler.BulkUpdateCampaigns(ctx, in, out)
}

func (h *marketServiceHandler) AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, out *AssignCompanyForCampaignsResponse) error {
	return h.MarketServiceHandler.AssignCompanyForCampaigns(ctx, in, out)
}

func (h *marketServiceHandler) BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, out *emptypb.Empty) error {
	return h.MarketServiceHandler.BulkDeleteCampaigns(ctx, in, out)
}

func (h *marketServiceHandler) ListMarketers(ctx context.Context, in *ListMarketersRequest, out *ListMarketersResponse) error {
	return h.MarketServiceHandler.ListMarketers(ctx, in, out)
}

func (h *marketServiceHandler) BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, out *BatchInsertAdAccountsResponse) error {
	return h.MarketServiceHandler.BatchInsertAdAccounts(ctx, in, out)
}

func (h *marketServiceHandler) BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, out *BatchInsertCampaignsResponse) error {
	return h.MarketServiceHandler.BatchInsertCampaigns(ctx, in, out)
}

func (h *marketServiceHandler) BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, out *BatchInsertAdsetsResponse) error {
	return h.MarketServiceHandler.BatchInsertAdsets(ctx, in, out)
}

func (h *marketServiceHandler) BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, out *BatchInsertAdsResponse) error {
	return h.MarketServiceHandler.BatchInsertAds(ctx, in, out)
}

func (h *marketServiceHandler) BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, out *BatchInsertSpendingsResponse) error {
	return h.MarketServiceHandler.BatchInsertSpendings(ctx, in, out)
}

func (h *marketServiceHandler) GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, out *GetTokenByAdIDResponse) error {
	return h.MarketServiceHandler.GetTokenByAdID(ctx, in, out)
}
