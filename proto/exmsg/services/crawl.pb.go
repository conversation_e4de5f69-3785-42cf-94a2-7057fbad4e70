// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/services/crawl.proto

package services

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CrawlRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AccessToken       string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	AnalyticAccountId int64                  `protobuf:"varint,2,opt,name=analytic_account_id,json=analyticAccountId,proto3" json:"analytic_account_id,omitempty"`
	MarketerId        int64                  `protobuf:"varint,3,opt,name=marketer_id,json=marketerId,proto3" json:"marketer_id,omitempty"`
	CompanyId         int64                  `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	ReAdd             bool                   `protobuf:"varint,5,opt,name=re_add,json=reAdd,proto3" json:"re_add,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CrawlRequest) Reset() {
	*x = CrawlRequest{}
	mi := &file_proto_services_crawl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrawlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrawlRequest) ProtoMessage() {}

func (x *CrawlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_crawl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrawlRequest.ProtoReflect.Descriptor instead.
func (*CrawlRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_crawl_proto_rawDescGZIP(), []int{0}
}

func (x *CrawlRequest) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *CrawlRequest) GetAnalyticAccountId() int64 {
	if x != nil {
		return x.AnalyticAccountId
	}
	return 0
}

func (x *CrawlRequest) GetMarketerId() int64 {
	if x != nil {
		return x.MarketerId
	}
	return 0
}

func (x *CrawlRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CrawlRequest) GetReAdd() bool {
	if x != nil {
		return x.ReAdd
	}
	return false
}

type CrawlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messsage      string                 `protobuf:"bytes,1,opt,name=messsage,proto3" json:"messsage,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CrawlResponse) Reset() {
	*x = CrawlResponse{}
	mi := &file_proto_services_crawl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrawlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrawlResponse) ProtoMessage() {}

func (x *CrawlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_crawl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrawlResponse.ProtoReflect.Descriptor instead.
func (*CrawlResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_crawl_proto_rawDescGZIP(), []int{1}
}

func (x *CrawlResponse) GetMesssage() string {
	if x != nil {
		return x.Messsage
	}
	return ""
}

func (x *CrawlResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TaskCore struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Metadata      *structpb.Struct       `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskCore) Reset() {
	*x = TaskCore{}
	mi := &file_proto_services_crawl_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskCore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCore) ProtoMessage() {}

func (x *TaskCore) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_crawl_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCore.ProtoReflect.Descriptor instead.
func (*TaskCore) Descriptor() ([]byte, []int) {
	return file_proto_services_crawl_proto_rawDescGZIP(), []int{2}
}

func (x *TaskCore) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskCore) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TaskCore) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type AdvancedCrawlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InitType      string                 `protobuf:"bytes,1,opt,name=init_type,json=initType,proto3" json:"init_type,omitempty"`
	Depth         int32                  `protobuf:"varint,2,opt,name=depth,proto3" json:"depth,omitempty"`
	MarketerId    int64                  `protobuf:"varint,3,opt,name=marketer_id,json=marketerId,proto3" json:"marketer_id,omitempty"`
	Tasks         []*TaskCore            `protobuf:"bytes,4,rep,name=tasks,proto3" json:"tasks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdvancedCrawlRequest) Reset() {
	*x = AdvancedCrawlRequest{}
	mi := &file_proto_services_crawl_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvancedCrawlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedCrawlRequest) ProtoMessage() {}

func (x *AdvancedCrawlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_crawl_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedCrawlRequest.ProtoReflect.Descriptor instead.
func (*AdvancedCrawlRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_crawl_proto_rawDescGZIP(), []int{3}
}

func (x *AdvancedCrawlRequest) GetInitType() string {
	if x != nil {
		return x.InitType
	}
	return ""
}

func (x *AdvancedCrawlRequest) GetDepth() int32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *AdvancedCrawlRequest) GetMarketerId() int64 {
	if x != nil {
		return x.MarketerId
	}
	return 0
}

func (x *AdvancedCrawlRequest) GetTasks() []*TaskCore {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type AdvancedCrawlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdvancedCrawlResponse) Reset() {
	*x = AdvancedCrawlResponse{}
	mi := &file_proto_services_crawl_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdvancedCrawlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedCrawlResponse) ProtoMessage() {}

func (x *AdvancedCrawlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_crawl_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedCrawlResponse.ProtoReflect.Descriptor instead.
func (*AdvancedCrawlResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_crawl_proto_rawDescGZIP(), []int{4}
}

func (x *AdvancedCrawlResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_proto_services_crawl_proto protoreflect.FileDescriptor

var file_proto_services_crawl_proto_rawDesc = string([]byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x01, 0x0a, 0x0c, 0x43,
	0x72, 0x61, 0x77, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x72, 0x65, 0x41, 0x64, 0x64, 0x22, 0x45, 0x0a, 0x0d, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x65, 0x0a, 0x08,
	0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x9a, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64,
	0x43, 0x72, 0x61, 0x77, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70,
	0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x72, 0x65, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x22, 0x31, 0x0a, 0x15, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x72, 0x61, 0x77,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x32, 0xb2, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x05, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x12, 0x1c, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43,
	0x72, 0x61, 0x77, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x72, 0x61,
	0x77, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x41, 0x64,
	0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x12, 0x24, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x41, 0x64, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x43, 0x72, 0x61, 0x77, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68,
	0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_services_crawl_proto_rawDescOnce sync.Once
	file_proto_services_crawl_proto_rawDescData []byte
)

func file_proto_services_crawl_proto_rawDescGZIP() []byte {
	file_proto_services_crawl_proto_rawDescOnce.Do(func() {
		file_proto_services_crawl_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_services_crawl_proto_rawDesc), len(file_proto_services_crawl_proto_rawDesc)))
	})
	return file_proto_services_crawl_proto_rawDescData
}

var file_proto_services_crawl_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_services_crawl_proto_goTypes = []any{
	(*CrawlRequest)(nil),          // 0: exmsg.services.CrawlRequest
	(*CrawlResponse)(nil),         // 1: exmsg.services.CrawlResponse
	(*TaskCore)(nil),              // 2: exmsg.services.TaskCore
	(*AdvancedCrawlRequest)(nil),  // 3: exmsg.services.AdvancedCrawlRequest
	(*AdvancedCrawlResponse)(nil), // 4: exmsg.services.AdvancedCrawlResponse
	(*structpb.Struct)(nil),       // 5: google.protobuf.Struct
}
var file_proto_services_crawl_proto_depIdxs = []int32{
	5, // 0: exmsg.services.TaskCore.metadata:type_name -> google.protobuf.Struct
	2, // 1: exmsg.services.AdvancedCrawlRequest.tasks:type_name -> exmsg.services.TaskCore
	0, // 2: exmsg.services.CrawlService.Crawl:input_type -> exmsg.services.CrawlRequest
	3, // 3: exmsg.services.CrawlService.AdvancedCrawl:input_type -> exmsg.services.AdvancedCrawlRequest
	1, // 4: exmsg.services.CrawlService.Crawl:output_type -> exmsg.services.CrawlResponse
	4, // 5: exmsg.services.CrawlService.AdvancedCrawl:output_type -> exmsg.services.AdvancedCrawlResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_services_crawl_proto_init() }
func file_proto_services_crawl_proto_init() {
	if File_proto_services_crawl_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_services_crawl_proto_rawDesc), len(file_proto_services_crawl_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_services_crawl_proto_goTypes,
		DependencyIndexes: file_proto_services_crawl_proto_depIdxs,
		MessageInfos:      file_proto_services_crawl_proto_msgTypes,
	}.Build()
	File_proto_services_crawl_proto = out.File
	file_proto_services_crawl_proto_goTypes = nil
	file_proto_services_crawl_proto_depIdxs = nil
}
