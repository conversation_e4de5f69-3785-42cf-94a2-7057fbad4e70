// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: services/agsale.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AGSaleService_CreateLead_FullMethodName            = "/exmsg.services.AGSaleService/CreateLead"
	AGSaleService_GetLeads_FullMethodName              = "/exmsg.services.AGSaleService/GetLeads"
	AGSaleService_GetOrders_FullMethodName             = "/exmsg.services.AGSaleService/GetOrders"
	AGSaleService_CreateOrders_FullMethodName          = "/exmsg.services.AGSaleService/CreateOrders"
	AGSaleService_UpdateLead_FullMethodName            = "/exmsg.services.AGSaleService/UpdateLead"
	AGSaleService_CreateLeadCareAndItem_FullMethodName = "/exmsg.services.AGSaleService/CreateLeadCareAndItem"
	AGSaleService_GetCareReasons_FullMethodName        = "/exmsg.services.AGSaleService/GetCareReasons"
	AGSaleService_GetOrderProducts_FullMethodName      = "/exmsg.services.AGSaleService/GetOrderProducts"
	AGSaleService_GetLandingPages_FullMethodName       = "/exmsg.services.AGSaleService/GetLandingPages"
	AGSaleService_GetOrderDisplayId_FullMethodName     = "/exmsg.services.AGSaleService/GetOrderDisplayId"
	AGSaleService_UpdateOrder_FullMethodName           = "/exmsg.services.AGSaleService/UpdateOrder"
	AGSaleService_RemoveDuplicateOrder_FullMethodName  = "/exmsg.services.AGSaleService/RemoveDuplicateOrder"
	AGSaleService_RemoveDuplicateLead_FullMethodName   = "/exmsg.services.AGSaleService/RemoveDuplicateLead"
)

// AGSaleServiceClient is the client API for AGSaleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AGSaleServiceClient interface {
	CreateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error)
	GetLeads(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error)
	GetOrders(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	CreateOrders(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	UpdateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error)
	CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error)
	GetCareReasons(ctx context.Context, in *CareReasonRequest, opts ...grpc.CallOption) (*CareReasonResponse, error)
	GetOrderProducts(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	GetLandingPages(ctx context.Context, in *LandingPageRequest, opts ...grpc.CallOption) (*LandingPageResponse, error)
	GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	UpdateOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	RemoveDuplicateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error)
}

type aGSaleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAGSaleServiceClient(cc grpc.ClientConnInterface) AGSaleServiceClient {
	return &aGSaleServiceClient{cc}
}

func (c *aGSaleServiceClient) CreateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeadResponse)
	err := c.cc.Invoke(ctx, AGSaleService_CreateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetLeads(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeadResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetOrders(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) CreateOrders(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_CreateOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) UpdateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeadResponse)
	err := c.cc.Invoke(ctx, AGSaleService_UpdateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) CreateLeadCareAndItem(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeadResponse)
	err := c.cc.Invoke(ctx, AGSaleService_CreateLeadCareAndItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetCareReasons(ctx context.Context, in *CareReasonRequest, opts ...grpc.CallOption) (*CareReasonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CareReasonResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetCareReasons_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetOrderProducts(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetOrderProducts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetLandingPages(ctx context.Context, in *LandingPageRequest, opts ...grpc.CallOption) (*LandingPageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LandingPageResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetLandingPages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) GetOrderDisplayId(ctx context.Context, in *OrderDisplayIdRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_GetOrderDisplayId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) UpdateOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_UpdateOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) RemoveDuplicateOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, AGSaleService_RemoveDuplicateOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aGSaleServiceClient) RemoveDuplicateLead(ctx context.Context, in *LeadRequest, opts ...grpc.CallOption) (*LeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeadResponse)
	err := c.cc.Invoke(ctx, AGSaleService_RemoveDuplicateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AGSaleServiceServer is the server API for AGSaleService service.
// All implementations must embed UnimplementedAGSaleServiceServer
// for forward compatibility.
type AGSaleServiceServer interface {
	CreateLead(context.Context, *LeadRequest) (*LeadResponse, error)
	GetLeads(context.Context, *LeadRequest) (*LeadResponse, error)
	GetOrders(context.Context, *OrderRequest) (*OrderResponse, error)
	CreateOrders(context.Context, *OrderRequest) (*OrderResponse, error)
	UpdateLead(context.Context, *LeadRequest) (*LeadResponse, error)
	CreateLeadCareAndItem(context.Context, *LeadRequest) (*LeadResponse, error)
	GetCareReasons(context.Context, *CareReasonRequest) (*CareReasonResponse, error)
	GetOrderProducts(context.Context, *OrderRequest) (*OrderResponse, error)
	GetLandingPages(context.Context, *LandingPageRequest) (*LandingPageResponse, error)
	GetOrderDisplayId(context.Context, *OrderDisplayIdRequest) (*OrderResponse, error)
	UpdateOrder(context.Context, *OrderRequest) (*OrderResponse, error)
	RemoveDuplicateOrder(context.Context, *OrderRequest) (*OrderResponse, error)
	RemoveDuplicateLead(context.Context, *LeadRequest) (*LeadResponse, error)
	mustEmbedUnimplementedAGSaleServiceServer()
}

// UnimplementedAGSaleServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAGSaleServiceServer struct{}

func (UnimplementedAGSaleServiceServer) CreateLead(context.Context, *LeadRequest) (*LeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLead not implemented")
}
func (UnimplementedAGSaleServiceServer) GetLeads(context.Context, *LeadRequest) (*LeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeads not implemented")
}
func (UnimplementedAGSaleServiceServer) GetOrders(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrders not implemented")
}
func (UnimplementedAGSaleServiceServer) CreateOrders(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrders not implemented")
}
func (UnimplementedAGSaleServiceServer) UpdateLead(context.Context, *LeadRequest) (*LeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLead not implemented")
}
func (UnimplementedAGSaleServiceServer) CreateLeadCareAndItem(context.Context, *LeadRequest) (*LeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLeadCareAndItem not implemented")
}
func (UnimplementedAGSaleServiceServer) GetCareReasons(context.Context, *CareReasonRequest) (*CareReasonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCareReasons not implemented")
}
func (UnimplementedAGSaleServiceServer) GetOrderProducts(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderProducts not implemented")
}
func (UnimplementedAGSaleServiceServer) GetLandingPages(context.Context, *LandingPageRequest) (*LandingPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLandingPages not implemented")
}
func (UnimplementedAGSaleServiceServer) GetOrderDisplayId(context.Context, *OrderDisplayIdRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderDisplayId not implemented")
}
func (UnimplementedAGSaleServiceServer) UpdateOrder(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrder not implemented")
}
func (UnimplementedAGSaleServiceServer) RemoveDuplicateOrder(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDuplicateOrder not implemented")
}
func (UnimplementedAGSaleServiceServer) RemoveDuplicateLead(context.Context, *LeadRequest) (*LeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDuplicateLead not implemented")
}
func (UnimplementedAGSaleServiceServer) mustEmbedUnimplementedAGSaleServiceServer() {}
func (UnimplementedAGSaleServiceServer) testEmbeddedByValue()                       {}

// UnsafeAGSaleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AGSaleServiceServer will
// result in compilation errors.
type UnsafeAGSaleServiceServer interface {
	mustEmbedUnimplementedAGSaleServiceServer()
}

func RegisterAGSaleServiceServer(s grpc.ServiceRegistrar, srv AGSaleServiceServer) {
	// If the following call pancis, it indicates UnimplementedAGSaleServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AGSaleService_ServiceDesc, srv)
}

func _AGSaleService_CreateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).CreateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_CreateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).CreateLead(ctx, req.(*LeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetLeads(ctx, req.(*LeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetOrders(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_CreateOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).CreateOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_CreateOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).CreateOrders(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_UpdateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).UpdateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_UpdateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).UpdateLead(ctx, req.(*LeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_CreateLeadCareAndItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).CreateLeadCareAndItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_CreateLeadCareAndItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).CreateLeadCareAndItem(ctx, req.(*LeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetCareReasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CareReasonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetCareReasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetCareReasons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetCareReasons(ctx, req.(*CareReasonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetOrderProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetOrderProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetOrderProducts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetOrderProducts(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetLandingPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LandingPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetLandingPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetLandingPages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetLandingPages(ctx, req.(*LandingPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_GetOrderDisplayId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderDisplayIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).GetOrderDisplayId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_GetOrderDisplayId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).GetOrderDisplayId(ctx, req.(*OrderDisplayIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_UpdateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).UpdateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_UpdateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).UpdateOrder(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_RemoveDuplicateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).RemoveDuplicateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_RemoveDuplicateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).RemoveDuplicateOrder(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AGSaleService_RemoveDuplicateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AGSaleServiceServer).RemoveDuplicateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AGSaleService_RemoveDuplicateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AGSaleServiceServer).RemoveDuplicateLead(ctx, req.(*LeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AGSaleService_ServiceDesc is the grpc.ServiceDesc for AGSaleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AGSaleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.AGSaleService",
	HandlerType: (*AGSaleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLead",
			Handler:    _AGSaleService_CreateLead_Handler,
		},
		{
			MethodName: "GetLeads",
			Handler:    _AGSaleService_GetLeads_Handler,
		},
		{
			MethodName: "GetOrders",
			Handler:    _AGSaleService_GetOrders_Handler,
		},
		{
			MethodName: "CreateOrders",
			Handler:    _AGSaleService_CreateOrders_Handler,
		},
		{
			MethodName: "UpdateLead",
			Handler:    _AGSaleService_UpdateLead_Handler,
		},
		{
			MethodName: "CreateLeadCareAndItem",
			Handler:    _AGSaleService_CreateLeadCareAndItem_Handler,
		},
		{
			MethodName: "GetCareReasons",
			Handler:    _AGSaleService_GetCareReasons_Handler,
		},
		{
			MethodName: "GetOrderProducts",
			Handler:    _AGSaleService_GetOrderProducts_Handler,
		},
		{
			MethodName: "GetLandingPages",
			Handler:    _AGSaleService_GetLandingPages_Handler,
		},
		{
			MethodName: "GetOrderDisplayId",
			Handler:    _AGSaleService_GetOrderDisplayId_Handler,
		},
		{
			MethodName: "UpdateOrder",
			Handler:    _AGSaleService_UpdateOrder_Handler,
		},
		{
			MethodName: "RemoveDuplicateOrder",
			Handler:    _AGSaleService_RemoveDuplicateOrder_Handler,
		},
		{
			MethodName: "RemoveDuplicateLead",
			Handler:    _AGSaleService_RemoveDuplicateLead_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/agsale.proto",
}
