// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: proto/services/market.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateAnalyticAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	MarketerId    int64                  `protobuf:"varint,2,opt,name=marketer_id,json=marketerId,proto3" json:"marketer_id,omitempty"`
	CompanyId     int64                  `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	MarketerName  string                 `protobuf:"bytes,4,opt,name=marketer_name,json=marketerName,proto3" json:"marketer_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAnalyticAccountRequest) Reset() {
	*x = CreateAnalyticAccountRequest{}
	mi := &file_proto_services_market_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAnalyticAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnalyticAccountRequest) ProtoMessage() {}

func (x *CreateAnalyticAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnalyticAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAnalyticAccountRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAnalyticAccountRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CreateAnalyticAccountRequest) GetMarketerId() int64 {
	if x != nil {
		return x.MarketerId
	}
	return 0
}

func (x *CreateAnalyticAccountRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAnalyticAccountRequest) GetMarketerName() string {
	if x != nil {
		return x.MarketerName
	}
	return ""
}

type CreateAnalyticAccountResponse struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	AnalyticAccount *models.AnalyticAccount `protobuf:"bytes,1,opt,name=analytic_account,json=analyticAccount,proto3" json:"analytic_account,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateAnalyticAccountResponse) Reset() {
	*x = CreateAnalyticAccountResponse{}
	mi := &file_proto_services_market_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAnalyticAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnalyticAccountResponse) ProtoMessage() {}

func (x *CreateAnalyticAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnalyticAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAnalyticAccountResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAnalyticAccountResponse) GetAnalyticAccount() *models.AnalyticAccount {
	if x != nil {
		return x.AnalyticAccount
	}
	return nil
}

type GetAnalyticAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAnalyticAccountRequest) Reset() {
	*x = GetAnalyticAccountRequest{}
	mi := &file_proto_services_market_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnalyticAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalyticAccountRequest) ProtoMessage() {}

func (x *GetAnalyticAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalyticAccountRequest.ProtoReflect.Descriptor instead.
func (*GetAnalyticAccountRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{2}
}

func (x *GetAnalyticAccountRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAnalyticAccountResponse struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	AnalyticAccount *models.AnalyticAccount `protobuf:"bytes,1,opt,name=analytic_account,json=analyticAccount,proto3" json:"analytic_account,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetAnalyticAccountResponse) Reset() {
	*x = GetAnalyticAccountResponse{}
	mi := &file_proto_services_market_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnalyticAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalyticAccountResponse) ProtoMessage() {}

func (x *GetAnalyticAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalyticAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAnalyticAccountResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{3}
}

func (x *GetAnalyticAccountResponse) GetAnalyticAccount() *models.AnalyticAccount {
	if x != nil {
		return x.AnalyticAccount
	}
	return nil
}

type ListAnalyticAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	CompanyId     int64                  `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	SortField     string                 `protobuf:"bytes,4,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc         bool                   `protobuf:"varint,5,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	SearchTerm    *string                `protobuf:"bytes,6,opt,name=search_term,json=searchTerm,proto3,oneof" json:"search_term,omitempty"`
	IsAvailable   *bool                  `protobuf:"varint,7,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	MarketerIds   []int64                `protobuf:"varint,8,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAnalyticAccountsRequest) Reset() {
	*x = ListAnalyticAccountsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAnalyticAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnalyticAccountsRequest) ProtoMessage() {}

func (x *ListAnalyticAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnalyticAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListAnalyticAccountsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{4}
}

func (x *ListAnalyticAccountsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAnalyticAccountsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListAnalyticAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAnalyticAccountsRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListAnalyticAccountsRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *ListAnalyticAccountsRequest) GetSearchTerm() string {
	if x != nil && x.SearchTerm != nil {
		return *x.SearchTerm
	}
	return ""
}

func (x *ListAnalyticAccountsRequest) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *ListAnalyticAccountsRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

type ListAnalyticAccountsResponse struct {
	state            protoimpl.MessageState    `protogen:"open.v1"`
	AnalyticAccounts []*models.AnalyticAccount `protobuf:"bytes,1,rep,name=analytic_accounts,json=analyticAccounts,proto3" json:"analytic_accounts,omitempty"`
	Total            int64                     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ListAnalyticAccountsResponse) Reset() {
	*x = ListAnalyticAccountsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAnalyticAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnalyticAccountsResponse) ProtoMessage() {}

func (x *ListAnalyticAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnalyticAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListAnalyticAccountsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{5}
}

func (x *ListAnalyticAccountsResponse) GetAnalyticAccounts() []*models.AnalyticAccount {
	if x != nil {
		return x.AnalyticAccounts
	}
	return nil
}

func (x *ListAnalyticAccountsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DeleteAnalyticAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	Ids           []int64                `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAnalyticAccountRequest) Reset() {
	*x = DeleteAnalyticAccountRequest{}
	mi := &file_proto_services_market_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAnalyticAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAnalyticAccountRequest) ProtoMessage() {}

func (x *DeleteAnalyticAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAnalyticAccountRequest.ProtoReflect.Descriptor instead.
func (*DeleteAnalyticAccountRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteAnalyticAccountRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeleteAnalyticAccountRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListAdAccountsRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Page               int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit              int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	CompanyId          int64                  `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	SortField          string                 `protobuf:"bytes,4,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc              bool                   `protobuf:"varint,5,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	SearchTerm         *string                `protobuf:"bytes,6,opt,name=search_term,json=searchTerm,proto3,oneof" json:"search_term,omitempty"`
	IsActive           *bool                  `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	MarketerIds        []int64                `protobuf:"varint,8,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	AnalyticAccountIds []int64                `protobuf:"varint,9,rep,packed,name=analytic_account_ids,json=analyticAccountIds,proto3" json:"analytic_account_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListAdAccountsRequest) Reset() {
	*x = ListAdAccountsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAdAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAdAccountsRequest) ProtoMessage() {}

func (x *ListAdAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAdAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListAdAccountsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{7}
}

func (x *ListAdAccountsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAdAccountsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListAdAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAdAccountsRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListAdAccountsRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *ListAdAccountsRequest) GetSearchTerm() string {
	if x != nil && x.SearchTerm != nil {
		return *x.SearchTerm
	}
	return ""
}

func (x *ListAdAccountsRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListAdAccountsRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *ListAdAccountsRequest) GetAnalyticAccountIds() []int64 {
	if x != nil {
		return x.AnalyticAccountIds
	}
	return nil
}

type ListAdAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Adaccounts    []*models.AdAccount    `protobuf:"bytes,1,rep,name=adaccounts,proto3" json:"adaccounts,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAdAccountsResponse) Reset() {
	*x = ListAdAccountsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAdAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAdAccountsResponse) ProtoMessage() {}

func (x *ListAdAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAdAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListAdAccountsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{8}
}

func (x *ListAdAccountsResponse) GetAdaccounts() []*models.AdAccount {
	if x != nil {
		return x.Adaccounts
	}
	return nil
}

func (x *ListAdAccountsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BulkUpdateAdAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	MarketerId    int64                  `protobuf:"varint,2,opt,name=marketer_id,json=marketerId,proto3" json:"marketer_id,omitempty"`
	UpdatedById   int64                  `protobuf:"varint,3,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	AdaccountIds  []int64                `protobuf:"varint,4,rep,packed,name=adaccount_ids,json=adaccountIds,proto3" json:"adaccount_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkUpdateAdAccountsRequest) Reset() {
	*x = BulkUpdateAdAccountsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkUpdateAdAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateAdAccountsRequest) ProtoMessage() {}

func (x *BulkUpdateAdAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateAdAccountsRequest.ProtoReflect.Descriptor instead.
func (*BulkUpdateAdAccountsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{9}
}

func (x *BulkUpdateAdAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BulkUpdateAdAccountsRequest) GetMarketerId() int64 {
	if x != nil {
		return x.MarketerId
	}
	return 0
}

func (x *BulkUpdateAdAccountsRequest) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *BulkUpdateAdAccountsRequest) GetAdaccountIds() []int64 {
	if x != nil {
		return x.AdaccountIds
	}
	return nil
}

type BulkUpdateAdAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdatedById   int64                  `protobuf:"varint,1,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkUpdateAdAccountsResponse) Reset() {
	*x = BulkUpdateAdAccountsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkUpdateAdAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateAdAccountsResponse) ProtoMessage() {}

func (x *BulkUpdateAdAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateAdAccountsResponse.ProtoReflect.Descriptor instead.
func (*BulkUpdateAdAccountsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{10}
}

func (x *BulkUpdateAdAccountsResponse) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *BulkUpdateAdAccountsResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type BulkDeleteAdAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	Ids           []int64                `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDeleteAdAccountsRequest) Reset() {
	*x = BulkDeleteAdAccountsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDeleteAdAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDeleteAdAccountsRequest) ProtoMessage() {}

func (x *BulkDeleteAdAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDeleteAdAccountsRequest.ProtoReflect.Descriptor instead.
func (*BulkDeleteAdAccountsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{11}
}

func (x *BulkDeleteAdAccountsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BulkDeleteAdAccountsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListCampaignsRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Page               int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit              int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	CompanyId          int64                  `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	SortField          string                 `protobuf:"bytes,4,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc              bool                   `protobuf:"varint,5,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	UserId             *int64                 `protobuf:"varint,6,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	SearchTerm         *string                `protobuf:"bytes,7,opt,name=search_term,json=searchTerm,proto3,oneof" json:"search_term,omitempty"`
	Status             *string                `protobuf:"bytes,8,opt,name=status,proto3,oneof" json:"status,omitempty"`
	MarketerIds        []int64                `protobuf:"varint,9,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	AnalyticAccountIds []int64                `protobuf:"varint,10,rep,packed,name=analytic_account_ids,json=analyticAccountIds,proto3" json:"analytic_account_ids,omitempty"`
	AdaccountIds       []int64                `protobuf:"varint,11,rep,packed,name=adaccount_ids,json=adaccountIds,proto3" json:"adaccount_ids,omitempty"`
	LandingIds         []string               `protobuf:"bytes,12,rep,name=landing_ids,json=landingIds,proto3" json:"landing_ids,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListCampaignsRequest) Reset() {
	*x = ListCampaignsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCampaignsRequest) ProtoMessage() {}

func (x *ListCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCampaignsRequest.ProtoReflect.Descriptor instead.
func (*ListCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{12}
}

func (x *ListCampaignsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCampaignsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListCampaignsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCampaignsRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListCampaignsRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *ListCampaignsRequest) GetUserId() int64 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *ListCampaignsRequest) GetSearchTerm() string {
	if x != nil && x.SearchTerm != nil {
		return *x.SearchTerm
	}
	return ""
}

func (x *ListCampaignsRequest) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *ListCampaignsRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *ListCampaignsRequest) GetAnalyticAccountIds() []int64 {
	if x != nil {
		return x.AnalyticAccountIds
	}
	return nil
}

func (x *ListCampaignsRequest) GetAdaccountIds() []int64 {
	if x != nil {
		return x.AdaccountIds
	}
	return nil
}

func (x *ListCampaignsRequest) GetLandingIds() []string {
	if x != nil {
		return x.LandingIds
	}
	return nil
}

type ListCampaignsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Campaigns     []*models.Campaign     `protobuf:"bytes,1,rep,name=campaigns,proto3" json:"campaigns,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	CountNotInBiz int64                  `protobuf:"varint,3,opt,name=count_not_in_biz,json=countNotInBiz,proto3" json:"count_not_in_biz,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCampaignsResponse) Reset() {
	*x = ListCampaignsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCampaignsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCampaignsResponse) ProtoMessage() {}

func (x *ListCampaignsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCampaignsResponse.ProtoReflect.Descriptor instead.
func (*ListCampaignsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{13}
}

func (x *ListCampaignsResponse) GetCampaigns() []*models.Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

func (x *ListCampaignsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListCampaignsResponse) GetCountNotInBiz() int64 {
	if x != nil {
		return x.CountNotInBiz
	}
	return 0
}

type LandingPage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LandingPage) Reset() {
	*x = LandingPage{}
	mi := &file_proto_services_market_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LandingPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LandingPage) ProtoMessage() {}

func (x *LandingPage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LandingPage.ProtoReflect.Descriptor instead.
func (*LandingPage) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{14}
}

func (x *LandingPage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LandingPage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BulkUpdateCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	MarketerId    *int64                 `protobuf:"varint,2,opt,name=marketer_id,json=marketerId,proto3,oneof" json:"marketer_id,omitempty"`
	UpdatedById   int64                  `protobuf:"varint,3,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	CampaignIds   []int64                `protobuf:"varint,4,rep,packed,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"`
	LandingId     *string                `protobuf:"bytes,5,opt,name=landing_id,json=landingId,proto3,oneof" json:"landing_id,omitempty"`
	LandingName   *string                `protobuf:"bytes,6,opt,name=landing_name,json=landingName,proto3,oneof" json:"landing_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkUpdateCampaignsRequest) Reset() {
	*x = BulkUpdateCampaignsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkUpdateCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateCampaignsRequest) ProtoMessage() {}

func (x *BulkUpdateCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateCampaignsRequest.ProtoReflect.Descriptor instead.
func (*BulkUpdateCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{15}
}

func (x *BulkUpdateCampaignsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BulkUpdateCampaignsRequest) GetMarketerId() int64 {
	if x != nil && x.MarketerId != nil {
		return *x.MarketerId
	}
	return 0
}

func (x *BulkUpdateCampaignsRequest) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *BulkUpdateCampaignsRequest) GetCampaignIds() []int64 {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

func (x *BulkUpdateCampaignsRequest) GetLandingId() string {
	if x != nil && x.LandingId != nil {
		return *x.LandingId
	}
	return ""
}

func (x *BulkUpdateCampaignsRequest) GetLandingName() string {
	if x != nil && x.LandingName != nil {
		return *x.LandingName
	}
	return ""
}

type BulkUpdateCampaignsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdatedById   int64                  `protobuf:"varint,1,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkUpdateCampaignsResponse) Reset() {
	*x = BulkUpdateCampaignsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkUpdateCampaignsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateCampaignsResponse) ProtoMessage() {}

func (x *BulkUpdateCampaignsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateCampaignsResponse.ProtoReflect.Descriptor instead.
func (*BulkUpdateCampaignsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{16}
}

func (x *BulkUpdateCampaignsResponse) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *BulkUpdateCampaignsResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AssignCompanyForCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Disable       bool                   `protobuf:"varint,1,opt,name=disable,proto3" json:"disable,omitempty"`
	UpdatedById   int64                  `protobuf:"varint,2,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	CampaignIds   []int64                `protobuf:"varint,3,rep,packed,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignCompanyForCampaignsRequest) Reset() {
	*x = AssignCompanyForCampaignsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignCompanyForCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignCompanyForCampaignsRequest) ProtoMessage() {}

func (x *AssignCompanyForCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignCompanyForCampaignsRequest.ProtoReflect.Descriptor instead.
func (*AssignCompanyForCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{17}
}

func (x *AssignCompanyForCampaignsRequest) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *AssignCompanyForCampaignsRequest) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *AssignCompanyForCampaignsRequest) GetCampaignIds() []int64 {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

type AssignCompanyForCampaignsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdatedById   int64                  `protobuf:"varint,1,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignCompanyForCampaignsResponse) Reset() {
	*x = AssignCompanyForCampaignsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignCompanyForCampaignsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignCompanyForCampaignsResponse) ProtoMessage() {}

func (x *AssignCompanyForCampaignsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignCompanyForCampaignsResponse.ProtoReflect.Descriptor instead.
func (*AssignCompanyForCampaignsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{18}
}

func (x *AssignCompanyForCampaignsResponse) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

type BulkDeleteCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	Ids           []int64                `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDeleteCampaignsRequest) Reset() {
	*x = BulkDeleteCampaignsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDeleteCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDeleteCampaignsRequest) ProtoMessage() {}

func (x *BulkDeleteCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDeleteCampaignsRequest.ProtoReflect.Descriptor instead.
func (*BulkDeleteCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{19}
}

func (x *BulkDeleteCampaignsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BulkDeleteCampaignsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ListMarketersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CompanyId     int64                  `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	SortField     string                 `protobuf:"bytes,2,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc         bool                   `protobuf:"varint,3,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMarketersRequest) Reset() {
	*x = ListMarketersRequest{}
	mi := &file_proto_services_market_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMarketersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMarketersRequest) ProtoMessage() {}

func (x *ListMarketersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMarketersRequest.ProtoReflect.Descriptor instead.
func (*ListMarketersRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{20}
}

func (x *ListMarketersRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListMarketersRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListMarketersRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

type ListMarketersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Marketers     []*models.Marketer     `protobuf:"bytes,1,rep,name=marketers,proto3" json:"marketers,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMarketersResponse) Reset() {
	*x = ListMarketersResponse{}
	mi := &file_proto_services_market_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMarketersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMarketersResponse) ProtoMessage() {}

func (x *ListMarketersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMarketersResponse.ProtoReflect.Descriptor instead.
func (*ListMarketersResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{21}
}

func (x *ListMarketersResponse) GetMarketers() []*models.Marketer {
	if x != nil {
		return x.Marketers
	}
	return nil
}

func (x *ListMarketersResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BatchInsertAdAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Adaccounts    []*models.AdAccount    `protobuf:"bytes,1,rep,name=adaccounts,proto3" json:"adaccounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdAccountsRequest) Reset() {
	*x = BatchInsertAdAccountsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdAccountsRequest) ProtoMessage() {}

func (x *BatchInsertAdAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdAccountsRequest.ProtoReflect.Descriptor instead.
func (*BatchInsertAdAccountsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{22}
}

func (x *BatchInsertAdAccountsRequest) GetAdaccounts() []*models.AdAccount {
	if x != nil {
		return x.Adaccounts
	}
	return nil
}

type BatchInsertAdAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdAccountsResponse) Reset() {
	*x = BatchInsertAdAccountsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdAccountsResponse) ProtoMessage() {}

func (x *BatchInsertAdAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdAccountsResponse.ProtoReflect.Descriptor instead.
func (*BatchInsertAdAccountsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{23}
}

func (x *BatchInsertAdAccountsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BatchInsertCampaignsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Campaigns     []*models.Campaign     `protobuf:"bytes,1,rep,name=campaigns,proto3" json:"campaigns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertCampaignsRequest) Reset() {
	*x = BatchInsertCampaignsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertCampaignsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertCampaignsRequest) ProtoMessage() {}

func (x *BatchInsertCampaignsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertCampaignsRequest.ProtoReflect.Descriptor instead.
func (*BatchInsertCampaignsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{24}
}

func (x *BatchInsertCampaignsRequest) GetCampaigns() []*models.Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

type BatchInsertCampaignsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertCampaignsResponse) Reset() {
	*x = BatchInsertCampaignsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertCampaignsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertCampaignsResponse) ProtoMessage() {}

func (x *BatchInsertCampaignsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertCampaignsResponse.ProtoReflect.Descriptor instead.
func (*BatchInsertCampaignsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{25}
}

func (x *BatchInsertCampaignsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BatchInsertAdsetsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Adsets        []*models.Adset        `protobuf:"bytes,1,rep,name=adsets,proto3" json:"adsets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdsetsRequest) Reset() {
	*x = BatchInsertAdsetsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdsetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdsetsRequest) ProtoMessage() {}

func (x *BatchInsertAdsetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdsetsRequest.ProtoReflect.Descriptor instead.
func (*BatchInsertAdsetsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{26}
}

func (x *BatchInsertAdsetsRequest) GetAdsets() []*models.Adset {
	if x != nil {
		return x.Adsets
	}
	return nil
}

type BatchInsertAdsetsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdsetsResponse) Reset() {
	*x = BatchInsertAdsetsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdsetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdsetsResponse) ProtoMessage() {}

func (x *BatchInsertAdsetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdsetsResponse.ProtoReflect.Descriptor instead.
func (*BatchInsertAdsetsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{27}
}

func (x *BatchInsertAdsetsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BatchInsertAdsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ads           []*models.Ad           `protobuf:"bytes,1,rep,name=ads,proto3" json:"ads,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdsRequest) Reset() {
	*x = BatchInsertAdsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdsRequest) ProtoMessage() {}

func (x *BatchInsertAdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdsRequest.ProtoReflect.Descriptor instead.
func (*BatchInsertAdsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{28}
}

func (x *BatchInsertAdsRequest) GetAds() []*models.Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

type BatchInsertAdsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertAdsResponse) Reset() {
	*x = BatchInsertAdsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertAdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertAdsResponse) ProtoMessage() {}

func (x *BatchInsertAdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertAdsResponse.ProtoReflect.Descriptor instead.
func (*BatchInsertAdsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{29}
}

func (x *BatchInsertAdsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BatchInsertSpendingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Spendings     []*models.Spending     `protobuf:"bytes,1,rep,name=spendings,proto3" json:"spendings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertSpendingsRequest) Reset() {
	*x = BatchInsertSpendingsRequest{}
	mi := &file_proto_services_market_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertSpendingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertSpendingsRequest) ProtoMessage() {}

func (x *BatchInsertSpendingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertSpendingsRequest.ProtoReflect.Descriptor instead.
func (*BatchInsertSpendingsRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{30}
}

func (x *BatchInsertSpendingsRequest) GetSpendings() []*models.Spending {
	if x != nil {
		return x.Spendings
	}
	return nil
}

type BatchInsertSpendingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchInsertSpendingsResponse) Reset() {
	*x = BatchInsertSpendingsResponse{}
	mi := &file_proto_services_market_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchInsertSpendingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchInsertSpendingsResponse) ProtoMessage() {}

func (x *BatchInsertSpendingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchInsertSpendingsResponse.ProtoReflect.Descriptor instead.
func (*BatchInsertSpendingsResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{31}
}

func (x *BatchInsertSpendingsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetTokenByAdIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AdId          int64                  `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTokenByAdIDRequest) Reset() {
	*x = GetTokenByAdIDRequest{}
	mi := &file_proto_services_market_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTokenByAdIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenByAdIDRequest) ProtoMessage() {}

func (x *GetTokenByAdIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenByAdIDRequest.ProtoReflect.Descriptor instead.
func (*GetTokenByAdIDRequest) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{32}
}

func (x *GetTokenByAdIDRequest) GetAdId() int64 {
	if x != nil {
		return x.AdId
	}
	return 0
}

type GetTokenByAdIDResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTokenByAdIDResponse) Reset() {
	*x = GetTokenByAdIDResponse{}
	mi := &file_proto_services_market_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTokenByAdIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenByAdIDResponse) ProtoMessage() {}

func (x *GetTokenByAdIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_services_market_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenByAdIDResponse.ProtoReflect.Descriptor instead.
func (*GetTokenByAdIDResponse) Descriptor() ([]byte, []int) {
	return file_proto_services_market_proto_rawDescGZIP(), []int{33}
}

func (x *GetTokenByAdIDResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_proto_services_market_proto protoreflect.FileDescriptor

var file_proto_services_market_proto_rawDesc = string([]byte{
	0x0a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x69, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2b,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x66, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x0f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xae, 0x02, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41,
	0x73, 0x63, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x65, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01,
	0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x11, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x10, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4f, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xd1, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73,
	0x5f, 0x61, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x73,
	0x63, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x65, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x08, 0x69, 0x73,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x67, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa6, 0x01, 0x0a, 0x1b, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0c, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x7d,
	0x0a, 0x1c, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4e, 0x0a,
	0x1b, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xb8, 0x03,
	0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x69, 0x73, 0x41, 0x73, 0x63, 0x12, 0x1c, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65,
	0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0c, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x27,
	0x0a, 0x10, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x62,
	0x69, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x6f, 0x74, 0x49, 0x6e, 0x42, 0x69, 0x7a, 0x22, 0x31, 0x0a, 0x0b, 0x4c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa4, 0x02, 0x0a, 0x1a, 0x42,
	0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x7c, 0x0a, 0x1b, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x83, 0x01, 0x0a, 0x20, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x47, 0x0a, 0x21, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x49, 0x64, 0x22, 0x4d,
	0x0a, 0x1a, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x6b, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x73, 0x63, 0x22, 0x63, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x52, 0x09,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x57, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x37, 0x0a, 0x0a, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x64,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x39, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x53, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x22, 0x38, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x47, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72,
	0x74, 0x41, 0x64, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b,
	0x0a, 0x06, 0x61, 0x64, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x64,
	0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x64, 0x73, 0x65, 0x74, 0x73, 0x22, 0x35, 0x0a, 0x19, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x73, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x3b, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72,
	0x74, 0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x03, 0x61,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x22,
	0x32, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x53, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x09, 0x73,
	0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x38, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x2c, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x79,
	0x41, 0x64, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x61,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64,
	0x22, 0x2e, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x79, 0x41, 0x64,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x32, 0x9a, 0x0e, 0x0a, 0x0d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x74, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x65, 0x78,
	0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x12, 0x2b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x15, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5f, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x25, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x14, 0x42,
	0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x2b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x14, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x2b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x0d, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x24, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x13, 0x42, 0x75, 0x6c,
	0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73,
	0x12, 0x2a, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x75,
	0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x6f, 0x72, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x30, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x13,
	0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x73, 0x12, 0x2a, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x24, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e,
	0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x2c,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x14, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x73, 0x12, 0x2b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68,
	0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x73,
	0x65, 0x74, 0x73, 0x12, 0x28, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x41, 0x64, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x73, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x73, 0x12, 0x25, 0x2e, 0x65, 0x78, 0x6d,
	0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x14, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x2b, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x53, 0x70,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x53, 0x70, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x49, 0x44, 0x12, 0x25,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x79, 0x41, 0x64, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42,
	0x79, 0x41, 0x64, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a,
	0x38, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32,
	0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_proto_services_market_proto_rawDescOnce sync.Once
	file_proto_services_market_proto_rawDescData []byte
)

func file_proto_services_market_proto_rawDescGZIP() []byte {
	file_proto_services_market_proto_rawDescOnce.Do(func() {
		file_proto_services_market_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_services_market_proto_rawDesc), len(file_proto_services_market_proto_rawDesc)))
	})
	return file_proto_services_market_proto_rawDescData
}

var file_proto_services_market_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_proto_services_market_proto_goTypes = []any{
	(*CreateAnalyticAccountRequest)(nil),      // 0: exmsg.services.CreateAnalyticAccountRequest
	(*CreateAnalyticAccountResponse)(nil),     // 1: exmsg.services.CreateAnalyticAccountResponse
	(*GetAnalyticAccountRequest)(nil),         // 2: exmsg.services.GetAnalyticAccountRequest
	(*GetAnalyticAccountResponse)(nil),        // 3: exmsg.services.GetAnalyticAccountResponse
	(*ListAnalyticAccountsRequest)(nil),       // 4: exmsg.services.ListAnalyticAccountsRequest
	(*ListAnalyticAccountsResponse)(nil),      // 5: exmsg.services.ListAnalyticAccountsResponse
	(*DeleteAnalyticAccountRequest)(nil),      // 6: exmsg.services.DeleteAnalyticAccountRequest
	(*ListAdAccountsRequest)(nil),             // 7: exmsg.services.ListAdAccountsRequest
	(*ListAdAccountsResponse)(nil),            // 8: exmsg.services.ListAdAccountsResponse
	(*BulkUpdateAdAccountsRequest)(nil),       // 9: exmsg.services.BulkUpdateAdAccountsRequest
	(*BulkUpdateAdAccountsResponse)(nil),      // 10: exmsg.services.BulkUpdateAdAccountsResponse
	(*BulkDeleteAdAccountsRequest)(nil),       // 11: exmsg.services.BulkDeleteAdAccountsRequest
	(*ListCampaignsRequest)(nil),              // 12: exmsg.services.ListCampaignsRequest
	(*ListCampaignsResponse)(nil),             // 13: exmsg.services.ListCampaignsResponse
	(*LandingPage)(nil),                       // 14: exmsg.services.LandingPage
	(*BulkUpdateCampaignsRequest)(nil),        // 15: exmsg.services.BulkUpdateCampaignsRequest
	(*BulkUpdateCampaignsResponse)(nil),       // 16: exmsg.services.BulkUpdateCampaignsResponse
	(*AssignCompanyForCampaignsRequest)(nil),  // 17: exmsg.services.AssignCompanyForCampaignsRequest
	(*AssignCompanyForCampaignsResponse)(nil), // 18: exmsg.services.AssignCompanyForCampaignsResponse
	(*BulkDeleteCampaignsRequest)(nil),        // 19: exmsg.services.BulkDeleteCampaignsRequest
	(*ListMarketersRequest)(nil),              // 20: exmsg.services.ListMarketersRequest
	(*ListMarketersResponse)(nil),             // 21: exmsg.services.ListMarketersResponse
	(*BatchInsertAdAccountsRequest)(nil),      // 22: exmsg.services.BatchInsertAdAccountsRequest
	(*BatchInsertAdAccountsResponse)(nil),     // 23: exmsg.services.BatchInsertAdAccountsResponse
	(*BatchInsertCampaignsRequest)(nil),       // 24: exmsg.services.BatchInsertCampaignsRequest
	(*BatchInsertCampaignsResponse)(nil),      // 25: exmsg.services.BatchInsertCampaignsResponse
	(*BatchInsertAdsetsRequest)(nil),          // 26: exmsg.services.BatchInsertAdsetsRequest
	(*BatchInsertAdsetsResponse)(nil),         // 27: exmsg.services.BatchInsertAdsetsResponse
	(*BatchInsertAdsRequest)(nil),             // 28: exmsg.services.BatchInsertAdsRequest
	(*BatchInsertAdsResponse)(nil),            // 29: exmsg.services.BatchInsertAdsResponse
	(*BatchInsertSpendingsRequest)(nil),       // 30: exmsg.services.BatchInsertSpendingsRequest
	(*BatchInsertSpendingsResponse)(nil),      // 31: exmsg.services.BatchInsertSpendingsResponse
	(*GetTokenByAdIDRequest)(nil),             // 32: exmsg.services.GetTokenByAdIDRequest
	(*GetTokenByAdIDResponse)(nil),            // 33: exmsg.services.GetTokenByAdIDResponse
	(*models.AnalyticAccount)(nil),            // 34: exmsg.models.AnalyticAccount
	(*models.AdAccount)(nil),                  // 35: exmsg.models.AdAccount
	(*timestamppb.Timestamp)(nil),             // 36: google.protobuf.Timestamp
	(*models.Campaign)(nil),                   // 37: exmsg.models.Campaign
	(*models.Marketer)(nil),                   // 38: exmsg.models.Marketer
	(*models.Adset)(nil),                      // 39: exmsg.models.Adset
	(*models.Ad)(nil),                         // 40: exmsg.models.Ad
	(*models.Spending)(nil),                   // 41: exmsg.models.Spending
	(*emptypb.Empty)(nil),                     // 42: google.protobuf.Empty
}
var file_proto_services_market_proto_depIdxs = []int32{
	34, // 0: exmsg.services.CreateAnalyticAccountResponse.analytic_account:type_name -> exmsg.models.AnalyticAccount
	34, // 1: exmsg.services.GetAnalyticAccountResponse.analytic_account:type_name -> exmsg.models.AnalyticAccount
	34, // 2: exmsg.services.ListAnalyticAccountsResponse.analytic_accounts:type_name -> exmsg.models.AnalyticAccount
	35, // 3: exmsg.services.ListAdAccountsResponse.adaccounts:type_name -> exmsg.models.AdAccount
	36, // 4: exmsg.services.BulkUpdateAdAccountsResponse.updated_at:type_name -> google.protobuf.Timestamp
	37, // 5: exmsg.services.ListCampaignsResponse.campaigns:type_name -> exmsg.models.Campaign
	36, // 6: exmsg.services.BulkUpdateCampaignsResponse.updated_at:type_name -> google.protobuf.Timestamp
	38, // 7: exmsg.services.ListMarketersResponse.marketers:type_name -> exmsg.models.Marketer
	35, // 8: exmsg.services.BatchInsertAdAccountsRequest.adaccounts:type_name -> exmsg.models.AdAccount
	37, // 9: exmsg.services.BatchInsertCampaignsRequest.campaigns:type_name -> exmsg.models.Campaign
	39, // 10: exmsg.services.BatchInsertAdsetsRequest.adsets:type_name -> exmsg.models.Adset
	40, // 11: exmsg.services.BatchInsertAdsRequest.ads:type_name -> exmsg.models.Ad
	41, // 12: exmsg.services.BatchInsertSpendingsRequest.spendings:type_name -> exmsg.models.Spending
	0,  // 13: exmsg.services.MarketService.CreateAnalyticAccount:input_type -> exmsg.services.CreateAnalyticAccountRequest
	4,  // 14: exmsg.services.MarketService.ListAnalyticAccounts:input_type -> exmsg.services.ListAnalyticAccountsRequest
	6,  // 15: exmsg.services.MarketService.DeleteAnalyticAccount:input_type -> exmsg.services.DeleteAnalyticAccountRequest
	7,  // 16: exmsg.services.MarketService.ListAdAccounts:input_type -> exmsg.services.ListAdAccountsRequest
	9,  // 17: exmsg.services.MarketService.BulkUpdateAdAccounts:input_type -> exmsg.services.BulkUpdateAdAccountsRequest
	11, // 18: exmsg.services.MarketService.BulkDeleteAdAccounts:input_type -> exmsg.services.BulkDeleteAdAccountsRequest
	12, // 19: exmsg.services.MarketService.ListCampaigns:input_type -> exmsg.services.ListCampaignsRequest
	15, // 20: exmsg.services.MarketService.BulkUpdateCampaigns:input_type -> exmsg.services.BulkUpdateCampaignsRequest
	17, // 21: exmsg.services.MarketService.AssignCompanyForCampaigns:input_type -> exmsg.services.AssignCompanyForCampaignsRequest
	19, // 22: exmsg.services.MarketService.BulkDeleteCampaigns:input_type -> exmsg.services.BulkDeleteCampaignsRequest
	20, // 23: exmsg.services.MarketService.ListMarketers:input_type -> exmsg.services.ListMarketersRequest
	22, // 24: exmsg.services.MarketService.BatchInsertAdAccounts:input_type -> exmsg.services.BatchInsertAdAccountsRequest
	24, // 25: exmsg.services.MarketService.BatchInsertCampaigns:input_type -> exmsg.services.BatchInsertCampaignsRequest
	26, // 26: exmsg.services.MarketService.BatchInsertAdsets:input_type -> exmsg.services.BatchInsertAdsetsRequest
	28, // 27: exmsg.services.MarketService.BatchInsertAds:input_type -> exmsg.services.BatchInsertAdsRequest
	30, // 28: exmsg.services.MarketService.BatchInsertSpendings:input_type -> exmsg.services.BatchInsertSpendingsRequest
	32, // 29: exmsg.services.MarketService.GetTokenByAdID:input_type -> exmsg.services.GetTokenByAdIDRequest
	1,  // 30: exmsg.services.MarketService.CreateAnalyticAccount:output_type -> exmsg.services.CreateAnalyticAccountResponse
	5,  // 31: exmsg.services.MarketService.ListAnalyticAccounts:output_type -> exmsg.services.ListAnalyticAccountsResponse
	42, // 32: exmsg.services.MarketService.DeleteAnalyticAccount:output_type -> google.protobuf.Empty
	8,  // 33: exmsg.services.MarketService.ListAdAccounts:output_type -> exmsg.services.ListAdAccountsResponse
	10, // 34: exmsg.services.MarketService.BulkUpdateAdAccounts:output_type -> exmsg.services.BulkUpdateAdAccountsResponse
	42, // 35: exmsg.services.MarketService.BulkDeleteAdAccounts:output_type -> google.protobuf.Empty
	13, // 36: exmsg.services.MarketService.ListCampaigns:output_type -> exmsg.services.ListCampaignsResponse
	16, // 37: exmsg.services.MarketService.BulkUpdateCampaigns:output_type -> exmsg.services.BulkUpdateCampaignsResponse
	18, // 38: exmsg.services.MarketService.AssignCompanyForCampaigns:output_type -> exmsg.services.AssignCompanyForCampaignsResponse
	42, // 39: exmsg.services.MarketService.BulkDeleteCampaigns:output_type -> google.protobuf.Empty
	21, // 40: exmsg.services.MarketService.ListMarketers:output_type -> exmsg.services.ListMarketersResponse
	23, // 41: exmsg.services.MarketService.BatchInsertAdAccounts:output_type -> exmsg.services.BatchInsertAdAccountsResponse
	25, // 42: exmsg.services.MarketService.BatchInsertCampaigns:output_type -> exmsg.services.BatchInsertCampaignsResponse
	27, // 43: exmsg.services.MarketService.BatchInsertAdsets:output_type -> exmsg.services.BatchInsertAdsetsResponse
	29, // 44: exmsg.services.MarketService.BatchInsertAds:output_type -> exmsg.services.BatchInsertAdsResponse
	31, // 45: exmsg.services.MarketService.BatchInsertSpendings:output_type -> exmsg.services.BatchInsertSpendingsResponse
	33, // 46: exmsg.services.MarketService.GetTokenByAdID:output_type -> exmsg.services.GetTokenByAdIDResponse
	30, // [30:47] is the sub-list for method output_type
	13, // [13:30] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proto_services_market_proto_init() }
func file_proto_services_market_proto_init() {
	if File_proto_services_market_proto != nil {
		return
	}
	file_proto_services_market_proto_msgTypes[4].OneofWrappers = []any{}
	file_proto_services_market_proto_msgTypes[7].OneofWrappers = []any{}
	file_proto_services_market_proto_msgTypes[12].OneofWrappers = []any{}
	file_proto_services_market_proto_msgTypes[15].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_services_market_proto_rawDesc), len(file_proto_services_market_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_services_market_proto_goTypes,
		DependencyIndexes: file_proto_services_market_proto_depIdxs,
		MessageInfos:      file_proto_services_market_proto_msgTypes,
	}.Build()
	File_proto_services_market_proto = out.File
	file_proto_services_market_proto_goTypes = nil
	file_proto_services_market_proto_depIdxs = nil
}
