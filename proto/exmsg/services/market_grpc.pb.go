// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/services/market.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MarketService_CreateAnalyticAccount_FullMethodName     = "/exmsg.services.MarketService/CreateAnalyticAccount"
	MarketService_ListAnalyticAccounts_FullMethodName      = "/exmsg.services.MarketService/ListAnalyticAccounts"
	MarketService_DeleteAnalyticAccount_FullMethodName     = "/exmsg.services.MarketService/DeleteAnalyticAccount"
	MarketService_ListAdAccounts_FullMethodName            = "/exmsg.services.MarketService/ListAdAccounts"
	MarketService_BulkUpdateAdAccounts_FullMethodName      = "/exmsg.services.MarketService/BulkUpdateAdAccounts"
	MarketService_BulkDeleteAdAccounts_FullMethodName      = "/exmsg.services.MarketService/BulkDeleteAdAccounts"
	MarketService_ListCampaigns_FullMethodName             = "/exmsg.services.MarketService/ListCampaigns"
	MarketService_BulkUpdateCampaigns_FullMethodName       = "/exmsg.services.MarketService/BulkUpdateCampaigns"
	MarketService_AssignCompanyForCampaigns_FullMethodName = "/exmsg.services.MarketService/AssignCompanyForCampaigns"
	MarketService_BulkDeleteCampaigns_FullMethodName       = "/exmsg.services.MarketService/BulkDeleteCampaigns"
	MarketService_ListMarketers_FullMethodName             = "/exmsg.services.MarketService/ListMarketers"
	MarketService_BatchInsertAdAccounts_FullMethodName     = "/exmsg.services.MarketService/BatchInsertAdAccounts"
	MarketService_BatchInsertCampaigns_FullMethodName      = "/exmsg.services.MarketService/BatchInsertCampaigns"
	MarketService_BatchInsertAdsets_FullMethodName         = "/exmsg.services.MarketService/BatchInsertAdsets"
	MarketService_BatchInsertAds_FullMethodName            = "/exmsg.services.MarketService/BatchInsertAds"
	MarketService_BatchInsertSpendings_FullMethodName      = "/exmsg.services.MarketService/BatchInsertSpendings"
	MarketService_GetTokenByAdID_FullMethodName            = "/exmsg.services.MarketService/GetTokenByAdID"
)

// MarketServiceClient is the client API for MarketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketServiceClient interface {
	CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, opts ...grpc.CallOption) (*CreateAnalyticAccountResponse, error)
	ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, opts ...grpc.CallOption) (*ListAnalyticAccountsResponse, error)
	DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, opts ...grpc.CallOption) (*ListAdAccountsResponse, error)
	BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, opts ...grpc.CallOption) (*BulkUpdateAdAccountsResponse, error)
	BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListCampaigns(ctx context.Context, in *ListCampaignsRequest, opts ...grpc.CallOption) (*ListCampaignsResponse, error)
	BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, opts ...grpc.CallOption) (*BulkUpdateCampaignsResponse, error)
	AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, opts ...grpc.CallOption) (*AssignCompanyForCampaignsResponse, error)
	BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListMarketers(ctx context.Context, in *ListMarketersRequest, opts ...grpc.CallOption) (*ListMarketersResponse, error)
	BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, opts ...grpc.CallOption) (*BatchInsertAdAccountsResponse, error)
	BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, opts ...grpc.CallOption) (*BatchInsertCampaignsResponse, error)
	BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, opts ...grpc.CallOption) (*BatchInsertAdsetsResponse, error)
	BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, opts ...grpc.CallOption) (*BatchInsertAdsResponse, error)
	BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, opts ...grpc.CallOption) (*BatchInsertSpendingsResponse, error)
	GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, opts ...grpc.CallOption) (*GetTokenByAdIDResponse, error)
}

type marketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketServiceClient(cc grpc.ClientConnInterface) MarketServiceClient {
	return &marketServiceClient{cc}
}

func (c *marketServiceClient) CreateAnalyticAccount(ctx context.Context, in *CreateAnalyticAccountRequest, opts ...grpc.CallOption) (*CreateAnalyticAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAnalyticAccountResponse)
	err := c.cc.Invoke(ctx, MarketService_CreateAnalyticAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ListAnalyticAccounts(ctx context.Context, in *ListAnalyticAccountsRequest, opts ...grpc.CallOption) (*ListAnalyticAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAnalyticAccountsResponse)
	err := c.cc.Invoke(ctx, MarketService_ListAnalyticAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) DeleteAnalyticAccount(ctx context.Context, in *DeleteAnalyticAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MarketService_DeleteAnalyticAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ListAdAccounts(ctx context.Context, in *ListAdAccountsRequest, opts ...grpc.CallOption) (*ListAdAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAdAccountsResponse)
	err := c.cc.Invoke(ctx, MarketService_ListAdAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BulkUpdateAdAccounts(ctx context.Context, in *BulkUpdateAdAccountsRequest, opts ...grpc.CallOption) (*BulkUpdateAdAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BulkUpdateAdAccountsResponse)
	err := c.cc.Invoke(ctx, MarketService_BulkUpdateAdAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BulkDeleteAdAccounts(ctx context.Context, in *BulkDeleteAdAccountsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MarketService_BulkDeleteAdAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ListCampaigns(ctx context.Context, in *ListCampaignsRequest, opts ...grpc.CallOption) (*ListCampaignsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCampaignsResponse)
	err := c.cc.Invoke(ctx, MarketService_ListCampaigns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BulkUpdateCampaigns(ctx context.Context, in *BulkUpdateCampaignsRequest, opts ...grpc.CallOption) (*BulkUpdateCampaignsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BulkUpdateCampaignsResponse)
	err := c.cc.Invoke(ctx, MarketService_BulkUpdateCampaigns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) AssignCompanyForCampaigns(ctx context.Context, in *AssignCompanyForCampaignsRequest, opts ...grpc.CallOption) (*AssignCompanyForCampaignsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignCompanyForCampaignsResponse)
	err := c.cc.Invoke(ctx, MarketService_AssignCompanyForCampaigns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BulkDeleteCampaigns(ctx context.Context, in *BulkDeleteCampaignsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MarketService_BulkDeleteCampaigns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ListMarketers(ctx context.Context, in *ListMarketersRequest, opts ...grpc.CallOption) (*ListMarketersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMarketersResponse)
	err := c.cc.Invoke(ctx, MarketService_ListMarketers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BatchInsertAdAccounts(ctx context.Context, in *BatchInsertAdAccountsRequest, opts ...grpc.CallOption) (*BatchInsertAdAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchInsertAdAccountsResponse)
	err := c.cc.Invoke(ctx, MarketService_BatchInsertAdAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BatchInsertCampaigns(ctx context.Context, in *BatchInsertCampaignsRequest, opts ...grpc.CallOption) (*BatchInsertCampaignsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchInsertCampaignsResponse)
	err := c.cc.Invoke(ctx, MarketService_BatchInsertCampaigns_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BatchInsertAdsets(ctx context.Context, in *BatchInsertAdsetsRequest, opts ...grpc.CallOption) (*BatchInsertAdsetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchInsertAdsetsResponse)
	err := c.cc.Invoke(ctx, MarketService_BatchInsertAdsets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BatchInsertAds(ctx context.Context, in *BatchInsertAdsRequest, opts ...grpc.CallOption) (*BatchInsertAdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchInsertAdsResponse)
	err := c.cc.Invoke(ctx, MarketService_BatchInsertAds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BatchInsertSpendings(ctx context.Context, in *BatchInsertSpendingsRequest, opts ...grpc.CallOption) (*BatchInsertSpendingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchInsertSpendingsResponse)
	err := c.cc.Invoke(ctx, MarketService_BatchInsertSpendings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetTokenByAdID(ctx context.Context, in *GetTokenByAdIDRequest, opts ...grpc.CallOption) (*GetTokenByAdIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTokenByAdIDResponse)
	err := c.cc.Invoke(ctx, MarketService_GetTokenByAdID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketServiceServer is the server API for MarketService service.
// All implementations must embed UnimplementedMarketServiceServer
// for forward compatibility.
type MarketServiceServer interface {
	CreateAnalyticAccount(context.Context, *CreateAnalyticAccountRequest) (*CreateAnalyticAccountResponse, error)
	ListAnalyticAccounts(context.Context, *ListAnalyticAccountsRequest) (*ListAnalyticAccountsResponse, error)
	DeleteAnalyticAccount(context.Context, *DeleteAnalyticAccountRequest) (*emptypb.Empty, error)
	ListAdAccounts(context.Context, *ListAdAccountsRequest) (*ListAdAccountsResponse, error)
	BulkUpdateAdAccounts(context.Context, *BulkUpdateAdAccountsRequest) (*BulkUpdateAdAccountsResponse, error)
	BulkDeleteAdAccounts(context.Context, *BulkDeleteAdAccountsRequest) (*emptypb.Empty, error)
	ListCampaigns(context.Context, *ListCampaignsRequest) (*ListCampaignsResponse, error)
	BulkUpdateCampaigns(context.Context, *BulkUpdateCampaignsRequest) (*BulkUpdateCampaignsResponse, error)
	AssignCompanyForCampaigns(context.Context, *AssignCompanyForCampaignsRequest) (*AssignCompanyForCampaignsResponse, error)
	BulkDeleteCampaigns(context.Context, *BulkDeleteCampaignsRequest) (*emptypb.Empty, error)
	ListMarketers(context.Context, *ListMarketersRequest) (*ListMarketersResponse, error)
	BatchInsertAdAccounts(context.Context, *BatchInsertAdAccountsRequest) (*BatchInsertAdAccountsResponse, error)
	BatchInsertCampaigns(context.Context, *BatchInsertCampaignsRequest) (*BatchInsertCampaignsResponse, error)
	BatchInsertAdsets(context.Context, *BatchInsertAdsetsRequest) (*BatchInsertAdsetsResponse, error)
	BatchInsertAds(context.Context, *BatchInsertAdsRequest) (*BatchInsertAdsResponse, error)
	BatchInsertSpendings(context.Context, *BatchInsertSpendingsRequest) (*BatchInsertSpendingsResponse, error)
	GetTokenByAdID(context.Context, *GetTokenByAdIDRequest) (*GetTokenByAdIDResponse, error)
	mustEmbedUnimplementedMarketServiceServer()
}

// UnimplementedMarketServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMarketServiceServer struct{}

func (UnimplementedMarketServiceServer) CreateAnalyticAccount(context.Context, *CreateAnalyticAccountRequest) (*CreateAnalyticAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAnalyticAccount not implemented")
}
func (UnimplementedMarketServiceServer) ListAnalyticAccounts(context.Context, *ListAnalyticAccountsRequest) (*ListAnalyticAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAnalyticAccounts not implemented")
}
func (UnimplementedMarketServiceServer) DeleteAnalyticAccount(context.Context, *DeleteAnalyticAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAnalyticAccount not implemented")
}
func (UnimplementedMarketServiceServer) ListAdAccounts(context.Context, *ListAdAccountsRequest) (*ListAdAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAdAccounts not implemented")
}
func (UnimplementedMarketServiceServer) BulkUpdateAdAccounts(context.Context, *BulkUpdateAdAccountsRequest) (*BulkUpdateAdAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkUpdateAdAccounts not implemented")
}
func (UnimplementedMarketServiceServer) BulkDeleteAdAccounts(context.Context, *BulkDeleteAdAccountsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkDeleteAdAccounts not implemented")
}
func (UnimplementedMarketServiceServer) ListCampaigns(context.Context, *ListCampaignsRequest) (*ListCampaignsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCampaigns not implemented")
}
func (UnimplementedMarketServiceServer) BulkUpdateCampaigns(context.Context, *BulkUpdateCampaignsRequest) (*BulkUpdateCampaignsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkUpdateCampaigns not implemented")
}
func (UnimplementedMarketServiceServer) AssignCompanyForCampaigns(context.Context, *AssignCompanyForCampaignsRequest) (*AssignCompanyForCampaignsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignCompanyForCampaigns not implemented")
}
func (UnimplementedMarketServiceServer) BulkDeleteCampaigns(context.Context, *BulkDeleteCampaignsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkDeleteCampaigns not implemented")
}
func (UnimplementedMarketServiceServer) ListMarketers(context.Context, *ListMarketersRequest) (*ListMarketersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMarketers not implemented")
}
func (UnimplementedMarketServiceServer) BatchInsertAdAccounts(context.Context, *BatchInsertAdAccountsRequest) (*BatchInsertAdAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchInsertAdAccounts not implemented")
}
func (UnimplementedMarketServiceServer) BatchInsertCampaigns(context.Context, *BatchInsertCampaignsRequest) (*BatchInsertCampaignsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchInsertCampaigns not implemented")
}
func (UnimplementedMarketServiceServer) BatchInsertAdsets(context.Context, *BatchInsertAdsetsRequest) (*BatchInsertAdsetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchInsertAdsets not implemented")
}
func (UnimplementedMarketServiceServer) BatchInsertAds(context.Context, *BatchInsertAdsRequest) (*BatchInsertAdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchInsertAds not implemented")
}
func (UnimplementedMarketServiceServer) BatchInsertSpendings(context.Context, *BatchInsertSpendingsRequest) (*BatchInsertSpendingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchInsertSpendings not implemented")
}
func (UnimplementedMarketServiceServer) GetTokenByAdID(context.Context, *GetTokenByAdIDRequest) (*GetTokenByAdIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokenByAdID not implemented")
}
func (UnimplementedMarketServiceServer) mustEmbedUnimplementedMarketServiceServer() {}
func (UnimplementedMarketServiceServer) testEmbeddedByValue()                       {}

// UnsafeMarketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketServiceServer will
// result in compilation errors.
type UnsafeMarketServiceServer interface {
	mustEmbedUnimplementedMarketServiceServer()
}

func RegisterMarketServiceServer(s grpc.ServiceRegistrar, srv MarketServiceServer) {
	// If the following call pancis, it indicates UnimplementedMarketServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MarketService_ServiceDesc, srv)
}

func _MarketService_CreateAnalyticAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAnalyticAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).CreateAnalyticAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_CreateAnalyticAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).CreateAnalyticAccount(ctx, req.(*CreateAnalyticAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ListAnalyticAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAnalyticAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ListAnalyticAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ListAnalyticAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ListAnalyticAccounts(ctx, req.(*ListAnalyticAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_DeleteAnalyticAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAnalyticAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).DeleteAnalyticAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_DeleteAnalyticAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).DeleteAnalyticAccount(ctx, req.(*DeleteAnalyticAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ListAdAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAdAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ListAdAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ListAdAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ListAdAccounts(ctx, req.(*ListAdAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BulkUpdateAdAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkUpdateAdAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BulkUpdateAdAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BulkUpdateAdAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BulkUpdateAdAccounts(ctx, req.(*BulkUpdateAdAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BulkDeleteAdAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkDeleteAdAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BulkDeleteAdAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BulkDeleteAdAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BulkDeleteAdAccounts(ctx, req.(*BulkDeleteAdAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ListCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCampaignsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ListCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ListCampaigns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ListCampaigns(ctx, req.(*ListCampaignsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BulkUpdateCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkUpdateCampaignsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BulkUpdateCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BulkUpdateCampaigns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BulkUpdateCampaigns(ctx, req.(*BulkUpdateCampaignsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_AssignCompanyForCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignCompanyForCampaignsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).AssignCompanyForCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_AssignCompanyForCampaigns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).AssignCompanyForCampaigns(ctx, req.(*AssignCompanyForCampaignsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BulkDeleteCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkDeleteCampaignsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BulkDeleteCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BulkDeleteCampaigns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BulkDeleteCampaigns(ctx, req.(*BulkDeleteCampaignsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ListMarketers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMarketersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ListMarketers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ListMarketers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ListMarketers(ctx, req.(*ListMarketersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BatchInsertAdAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchInsertAdAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BatchInsertAdAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BatchInsertAdAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BatchInsertAdAccounts(ctx, req.(*BatchInsertAdAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BatchInsertCampaigns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchInsertCampaignsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BatchInsertCampaigns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BatchInsertCampaigns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BatchInsertCampaigns(ctx, req.(*BatchInsertCampaignsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BatchInsertAdsets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchInsertAdsetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BatchInsertAdsets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BatchInsertAdsets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BatchInsertAdsets(ctx, req.(*BatchInsertAdsetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BatchInsertAds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchInsertAdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BatchInsertAds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BatchInsertAds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BatchInsertAds(ctx, req.(*BatchInsertAdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BatchInsertSpendings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchInsertSpendingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BatchInsertSpendings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BatchInsertSpendings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BatchInsertSpendings(ctx, req.(*BatchInsertSpendingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetTokenByAdID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenByAdIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetTokenByAdID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetTokenByAdID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetTokenByAdID(ctx, req.(*GetTokenByAdIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketService_ServiceDesc is the grpc.ServiceDesc for MarketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.MarketService",
	HandlerType: (*MarketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAnalyticAccount",
			Handler:    _MarketService_CreateAnalyticAccount_Handler,
		},
		{
			MethodName: "ListAnalyticAccounts",
			Handler:    _MarketService_ListAnalyticAccounts_Handler,
		},
		{
			MethodName: "DeleteAnalyticAccount",
			Handler:    _MarketService_DeleteAnalyticAccount_Handler,
		},
		{
			MethodName: "ListAdAccounts",
			Handler:    _MarketService_ListAdAccounts_Handler,
		},
		{
			MethodName: "BulkUpdateAdAccounts",
			Handler:    _MarketService_BulkUpdateAdAccounts_Handler,
		},
		{
			MethodName: "BulkDeleteAdAccounts",
			Handler:    _MarketService_BulkDeleteAdAccounts_Handler,
		},
		{
			MethodName: "ListCampaigns",
			Handler:    _MarketService_ListCampaigns_Handler,
		},
		{
			MethodName: "BulkUpdateCampaigns",
			Handler:    _MarketService_BulkUpdateCampaigns_Handler,
		},
		{
			MethodName: "AssignCompanyForCampaigns",
			Handler:    _MarketService_AssignCompanyForCampaigns_Handler,
		},
		{
			MethodName: "BulkDeleteCampaigns",
			Handler:    _MarketService_BulkDeleteCampaigns_Handler,
		},
		{
			MethodName: "ListMarketers",
			Handler:    _MarketService_ListMarketers_Handler,
		},
		{
			MethodName: "BatchInsertAdAccounts",
			Handler:    _MarketService_BatchInsertAdAccounts_Handler,
		},
		{
			MethodName: "BatchInsertCampaigns",
			Handler:    _MarketService_BatchInsertCampaigns_Handler,
		},
		{
			MethodName: "BatchInsertAdsets",
			Handler:    _MarketService_BatchInsertAdsets_Handler,
		},
		{
			MethodName: "BatchInsertAds",
			Handler:    _MarketService_BatchInsertAds_Handler,
		},
		{
			MethodName: "BatchInsertSpendings",
			Handler:    _MarketService_BatchInsertSpendings_Handler,
		},
		{
			MethodName: "GetTokenByAdID",
			Handler:    _MarketService_GetTokenByAdID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/services/market.proto",
}
