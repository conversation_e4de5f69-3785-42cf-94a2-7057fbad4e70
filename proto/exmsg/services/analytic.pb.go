// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.12.4
// source: services/analytic.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AnalyticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ViewName      string                 `protobuf:"bytes,1,opt,name=view_name,json=viewName,proto3" json:"view_name,omitempty"`
	Fields        []string               `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	Conditions    []*models.Condition    `protobuf:"bytes,3,rep,name=conditions,proto3" json:"conditions,omitempty"`
	GroupBy       []string               `protobuf:"bytes,4,rep,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	OrderBy       []string               `protobuf:"bytes,5,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Limit         uint64                 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        uint64                 `protobuf:"varint,7,opt,name=offset,proto3" json:"offset,omitempty"`
	CompanyId     int64                  `protobuf:"varint,8,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CountryId     int64                  `protobuf:"varint,9,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	StartTime     string                 `protobuf:"bytes,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TeamInCharge  int64                  `protobuf:"varint,12,opt,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	ProjectIds    []int64                `protobuf:"varint,13,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	MarketerIds   []int64                `protobuf:"varint,14,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	ProductIds    []int64                `protobuf:"varint,15,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	CampaignIds   []string               `protobuf:"bytes,16,rep,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"`
	AdaccountIds  []string               `protobuf:"bytes,17,rep,name=adaccount_ids,json=adaccountIds,proto3" json:"adaccount_ids,omitempty"`
	AdsetIds      []string               `protobuf:"bytes,18,rep,name=adset_ids,json=adsetIds,proto3" json:"adset_ids,omitempty"`
	IsSummary     bool                   `protobuf:"varint,19,opt,name=is_summary,json=isSummary,proto3" json:"is_summary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsRequest) Reset() {
	*x = AnalyticsRequest{}
	mi := &file_services_analytic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsRequest) ProtoMessage() {}

func (x *AnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsRequest.ProtoReflect.Descriptor instead.
func (*AnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{0}
}

func (x *AnalyticsRequest) GetViewName() string {
	if x != nil {
		return x.ViewName
	}
	return ""
}

func (x *AnalyticsRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *AnalyticsRequest) GetConditions() []*models.Condition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *AnalyticsRequest) GetGroupBy() []string {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *AnalyticsRequest) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *AnalyticsRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *AnalyticsRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *AnalyticsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AnalyticsRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *AnalyticsRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AnalyticsRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *AnalyticsRequest) GetTeamInCharge() int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return 0
}

func (x *AnalyticsRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *AnalyticsRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *AnalyticsRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *AnalyticsRequest) GetCampaignIds() []string {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

func (x *AnalyticsRequest) GetAdaccountIds() []string {
	if x != nil {
		return x.AdaccountIds
	}
	return nil
}

func (x *AnalyticsRequest) GetAdsetIds() []string {
	if x != nil {
		return x.AdsetIds
	}
	return nil
}

func (x *AnalyticsRequest) GetIsSummary() bool {
	if x != nil {
		return x.IsSummary
	}
	return false
}

type AnalyticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*models.Row          `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsResponse) Reset() {
	*x = AnalyticsResponse{}
	mi := &file_services_analytic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsResponse) ProtoMessage() {}

func (x *AnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsResponse.ProtoReflect.Descriptor instead.
func (*AnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{1}
}

func (x *AnalyticsResponse) GetData() []*models.Row {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AnalyticsResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type OrderDashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartTime     string                 `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ProjectIds    []int64                `protobuf:"varint,3,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	SaleReps      []int64                `protobuf:"varint,4,rep,packed,name=sale_reps,json=saleReps,proto3" json:"sale_reps,omitempty"`
	ProductIds    []int64                `protobuf:"varint,5,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	MarketerIds   []int64                `protobuf:"varint,6,rep,packed,name=marketer_ids,json=marketerIds,proto3" json:"marketer_ids,omitempty"`
	Sources       []string               `protobuf:"bytes,7,rep,name=sources,proto3" json:"sources,omitempty"`
	CarriersCode  []string               `protobuf:"bytes,8,rep,name=carriers_code,json=carriersCode,proto3" json:"carriers_code,omitempty"`
	TagIds        []int64                `protobuf:"varint,9,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	CompanyId     int64                  `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	CountryId     int64                  `protobuf:"varint,11,opt,name=country_id,json=countryId,proto3" json:"country_id,omitempty"`
	GroupBy       []string               `protobuf:"bytes,12,rep,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	OrderBy       []string               `protobuf:"bytes,13,rep,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Limit         uint64                 `protobuf:"varint,14,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        uint64                 `protobuf:"varint,15,opt,name=offset,proto3" json:"offset,omitempty"`
	DateRangeType string                 `protobuf:"bytes,16,opt,name=date_range_type,json=dateRangeType,proto3" json:"date_range_type,omitempty"`
	IsViewDetail  bool                   `protobuf:"varint,17,opt,name=is_view_detail,json=isViewDetail,proto3" json:"is_view_detail,omitempty"`
	Action        string                 `protobuf:"bytes,18,opt,name=action,proto3" json:"action,omitempty"`
	SaleId        []int64                `protobuf:"varint,19,rep,packed,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	CarePageId    []int64                `protobuf:"varint,20,rep,packed,name=care_page_id,json=carePageId,proto3" json:"care_page_id,omitempty"`
	TeamInCharge  []int64                `protobuf:"varint,21,rep,packed,name=team_in_charge,json=teamInCharge,proto3" json:"team_in_charge,omitempty"`
	OrderStatus   []int64                `protobuf:"varint,22,rep,packed,name=order_status,json=orderStatus,proto3" json:"order_status,omitempty"`
	Dids          []int64                `protobuf:"varint,23,rep,packed,name=dids,proto3" json:"dids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDashboardRequest) Reset() {
	*x = OrderDashboardRequest{}
	mi := &file_services_analytic_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDashboardRequest) ProtoMessage() {}

func (x *OrderDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_analytic_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDashboardRequest.ProtoReflect.Descriptor instead.
func (*OrderDashboardRequest) Descriptor() ([]byte, []int) {
	return file_services_analytic_proto_rawDescGZIP(), []int{2}
}

func (x *OrderDashboardRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *OrderDashboardRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *OrderDashboardRequest) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetSaleReps() []int64 {
	if x != nil {
		return x.SaleReps
	}
	return nil
}

func (x *OrderDashboardRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetMarketerIds() []int64 {
	if x != nil {
		return x.MarketerIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetSources() []string {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *OrderDashboardRequest) GetCarriersCode() []string {
	if x != nil {
		return x.CarriersCode
	}
	return nil
}

func (x *OrderDashboardRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *OrderDashboardRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderDashboardRequest) GetCountryId() int64 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *OrderDashboardRequest) GetGroupBy() []string {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *OrderDashboardRequest) GetOrderBy() []string {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *OrderDashboardRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *OrderDashboardRequest) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *OrderDashboardRequest) GetDateRangeType() string {
	if x != nil {
		return x.DateRangeType
	}
	return ""
}

func (x *OrderDashboardRequest) GetIsViewDetail() bool {
	if x != nil {
		return x.IsViewDetail
	}
	return false
}

func (x *OrderDashboardRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *OrderDashboardRequest) GetSaleId() []int64 {
	if x != nil {
		return x.SaleId
	}
	return nil
}

func (x *OrderDashboardRequest) GetCarePageId() []int64 {
	if x != nil {
		return x.CarePageId
	}
	return nil
}

func (x *OrderDashboardRequest) GetTeamInCharge() []int64 {
	if x != nil {
		return x.TeamInCharge
	}
	return nil
}

func (x *OrderDashboardRequest) GetOrderStatus() []int64 {
	if x != nil {
		return x.OrderStatus
	}
	return nil
}

func (x *OrderDashboardRequest) GetDids() []int64 {
	if x != nil {
		return x.Dids
	}
	return nil
}

var File_services_analytic_proto protoreflect.FileDescriptor

var file_services_analytic_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xeb,
	0x04, 0x0a, 0x10, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x65, 0x77, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x50, 0x0a, 0x11,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x52,
	0x6f, 0x77, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xcb,
	0x05, 0x0a, 0x15, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x73, 0x61, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x73, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x26,
	0x0a, 0x0f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x69, 0x73, 0x56, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x13, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x73, 0x61, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0c, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x65, 0x50, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x18, 0x15, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x64, 0x73,
	0x18, 0x17, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x64, 0x69, 0x64, 0x73, 0x32, 0x9c, 0x02, 0x0a,
	0x0f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x52, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x20, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x20, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x67, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x12, 0x25, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f,
	0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_analytic_proto_rawDescOnce sync.Once
	file_services_analytic_proto_rawDescData []byte
)

func file_services_analytic_proto_rawDescGZIP() []byte {
	file_services_analytic_proto_rawDescOnce.Do(func() {
		file_services_analytic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_analytic_proto_rawDesc), len(file_services_analytic_proto_rawDesc)))
	})
	return file_services_analytic_proto_rawDescData
}

var file_services_analytic_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_services_analytic_proto_goTypes = []any{
	(*AnalyticsRequest)(nil),      // 0: exmsg.services.AnalyticsRequest
	(*AnalyticsResponse)(nil),     // 1: exmsg.services.AnalyticsResponse
	(*OrderDashboardRequest)(nil), // 2: exmsg.services.OrderDashboardRequest
	(*models.Condition)(nil),      // 3: exmsg.models.Condition
	(*models.Row)(nil),            // 4: exmsg.models.Row
}
var file_services_analytic_proto_depIdxs = []int32{
	3, // 0: exmsg.services.AnalyticsRequest.conditions:type_name -> exmsg.models.Condition
	4, // 1: exmsg.services.AnalyticsResponse.data:type_name -> exmsg.models.Row
	0, // 2: exmsg.services.AnalyticService.QueryReport:input_type -> exmsg.services.AnalyticsRequest
	0, // 3: exmsg.services.AnalyticService.Query:input_type -> exmsg.services.AnalyticsRequest
	2, // 4: exmsg.services.AnalyticService.QueryOrderDashboardOverview:input_type -> exmsg.services.OrderDashboardRequest
	1, // 5: exmsg.services.AnalyticService.QueryReport:output_type -> exmsg.services.AnalyticsResponse
	1, // 6: exmsg.services.AnalyticService.Query:output_type -> exmsg.services.AnalyticsResponse
	1, // 7: exmsg.services.AnalyticService.QueryOrderDashboardOverview:output_type -> exmsg.services.AnalyticsResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_services_analytic_proto_init() }
func file_services_analytic_proto_init() {
	if File_services_analytic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_analytic_proto_rawDesc), len(file_services_analytic_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_analytic_proto_goTypes,
		DependencyIndexes: file_services_analytic_proto_depIdxs,
		MessageInfos:      file_services_analytic_proto_msgTypes,
	}.Build()
	File_services_analytic_proto = out.File
	file_services_analytic_proto_goTypes = nil
	file_services_analytic_proto_depIdxs = nil
}
