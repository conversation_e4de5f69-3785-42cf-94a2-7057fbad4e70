syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/common.proto";

service AnalyticService {
  rpc QueryReport (AnalyticsRequest) returns (AnalyticsResponse);
  rpc Query (AnalyticsRequest) returns (AnalyticsResponse);
  rpc QueryOrderDashboardOverview(OrderDashboardRequest) returns (AnalyticsResponse);
}

message AnalyticsRequest {
  string view_name = 1;
  repeated string fields = 2;
  repeated exmsg.models.Condition conditions = 3;
  repeated string group_by = 4;
  repeated string order_by = 5;
  uint64 limit = 6;
  uint64 offset = 7;

  int64 company_id = 8;
  int64 country_id = 9;
  string start_time = 10;
  string end_time = 11;
  int64 team_in_charge = 12;
  repeated int64 project_ids = 13;
  repeated int64 marketer_ids = 14;
  repeated int64 product_ids = 15;
  repeated string campaign_ids = 16;
  repeated string adaccount_ids = 17;
  repeated string adset_ids = 18;
  bool is_summary = 19;
}

message AnalyticsResponse {
  repeated exmsg.models.Row data = 1;
  string error = 2;
}

message OrderDashboardRequest {
  string start_time = 1;
  string end_time = 2;
  repeated int64 project_ids = 3;
  repeated int64 sale_reps = 4;
  repeated int64 product_ids = 5;
  repeated int64 marketer_ids = 6;
  repeated string sources = 7;
  repeated string carriers_code = 8;
  repeated int64 tag_ids = 9;
  int64 company_id = 10;
  int64 country_id = 11;
  repeated string group_by = 12;
  repeated string order_by = 13;
  uint64 limit = 14;
  uint64 offset = 15;
  string date_range_type = 16;
  bool is_view_detail = 17;
  string action = 18;
  repeated int64 sale_id = 19;
  repeated int64 care_page_id = 20;
  repeated int64 team_in_charge = 21;
  repeated int64 order_status = 22;
  repeated int64 dids = 23;
}
