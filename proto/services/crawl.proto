syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "google/protobuf/struct.proto";

service CrawlService {
  rpc Crawl (CrawlRequest) returns (CrawlResponse);
  rpc AdvancedCrawl(AdvancedCrawlRequest) returns (AdvancedCrawlResponse);
}

message CrawlRequest {
  string access_token = 1;
  int64 analytic_account_id = 2;
  int64 marketer_id = 3;
  int64 company_id = 4;
  bool re_add = 5;
}

message CrawlResponse {
  string messsage = 1;
  bool success = 2;
}

message TaskCore {
  int64 id = 1;
  string token = 2;
  google.protobuf.Struct metadata = 4;
}

message AdvancedCrawlRequest {
  string init_type = 1;
  int32 depth = 2;
  int64 marketer_id = 3;
  repeated TaskCore tasks = 4;
}

message AdvancedCrawlResponse {
  bool success = 1;
}