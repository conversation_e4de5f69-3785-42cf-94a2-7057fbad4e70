syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "proto/models/analytic_account.proto";
import "proto/models/ad_account.proto";
import "proto/models/campaign.proto";
import "proto/models/marketer.proto";
import "proto/models/adset.proto";
import "proto/models/ad.proto";
import "proto/models/spending.proto";

service MarketService {
  rpc CreateAnalyticAccount(CreateAnalyticAccountRequest) returns (CreateAnalyticAccountResponse);
  rpc ListAnalyticAccounts(ListAnalyticAccountsRequest) returns (ListAnalyticAccountsResponse);
  rpc DeleteAnalyticAccount(DeleteAnalyticAccountRequest) returns (google.protobuf.Empty);

  rpc ListAdAccounts(ListAdAccountsRequest) returns (ListAdAccountsResponse);
  rpc BulkUpdateAdAccounts(BulkUpdateAdAccountsRequest) returns (BulkUpdateAdAccountsResponse);
  rpc BulkDeleteAdAccounts(BulkDeleteAdAccountsRequest) returns (google.protobuf.Empty);

  rpc ListCampaigns(ListCampaignsRequest) returns (ListCampaignsResponse);
  rpc BulkUpdateCampaigns(BulkUpdateCampaignsRequest) returns (BulkUpdateCampaignsResponse);
  rpc AssignCompanyForCampaign(AssignCompanyForCampaignRequest) returns (AssignCompanyForCampaignResponse);
  rpc BulkDeleteCampaigns(BulkDeleteCampaignsRequest) returns (google.protobuf.Empty);

  rpc ListMarketers(ListMarketersRequest) returns (ListMarketersResponse);

  rpc BatchInsertAdAccounts(BatchInsertAdAccountsRequest) returns (BatchInsertAdAccountsResponse);
  rpc BatchInsertCampaigns(BatchInsertCampaignsRequest) returns (BatchInsertCampaignsResponse);
  rpc BatchInsertAdsets(BatchInsertAdsetsRequest) returns (BatchInsertAdsetsResponse);
  rpc BatchInsertAds(BatchInsertAdsRequest) returns (BatchInsertAdsResponse);
  rpc BatchInsertSpendings(BatchInsertSpendingsRequest) returns (BatchInsertSpendingsResponse);

  rpc GetTokenByAdID(GetTokenByAdIDRequest) returns (GetTokenByAdIDResponse);
}

message CreateAnalyticAccountRequest {
  string token = 1;
  int64 marketer_id = 2;
  int64 company_id = 3;
  string marketer_name = 4;
}

message CreateAnalyticAccountResponse {
  exmsg.models.AnalyticAccount analytic_account = 1;
}

message GetAnalyticAccountRequest {
  int64 id = 1;
}

message GetAnalyticAccountResponse {
  exmsg.models.AnalyticAccount analytic_account = 1;
}

message ListAnalyticAccountsRequest {
  int32 page = 1;
  int32 limit = 2;
  int64 company_id = 3;
  string sort_field = 4;
  bool is_asc = 5;
  optional string search_term = 6;
  optional bool is_available = 7;
  repeated int64 marketer_ids = 8;
}

message ListAnalyticAccountsResponse {
  repeated exmsg.models.AnalyticAccount analytic_accounts = 1;
  int64 total = 2;
}

message DeleteAnalyticAccountRequest {
  int64 company_id = 1;
  repeated int64 ids = 2;
}

message ListAdAccountsRequest {
  int32 page = 1;
  int32 limit = 2;
  int64 company_id = 3;
  string sort_field = 4;
  bool is_asc = 5;
  optional string search_term = 6;
  optional bool is_active = 7;
  repeated int64 marketer_ids = 8;
  repeated int64 analytic_account_ids = 9;
}

message ListAdAccountsResponse {
  repeated exmsg.models.AdAccount adaccounts = 1;
  int64 total = 2;
}

message BulkUpdateAdAccountsRequest {
  int64 company_id = 1;
  int64 marketer_id = 2;
  int64 updated_by_id = 3;
  repeated int64 adaccount_ids = 4;
}

message BulkUpdateAdAccountsResponse {
  int64 updated_by_id = 1;
  google.protobuf.Timestamp updated_at = 2;
}

message BulkDeleteAdAccountsRequest {
  int64 company_id = 1;
  repeated int64 ids = 2;
}

message ListCampaignsRequest {
  int32 page = 1;
  int32 limit = 2;
  int64 company_id = 3;
  string sort_field = 4;
  bool is_asc = 5;
  optional int64 user_id = 6;
  optional string search_term = 7;
  optional string status = 8;
  repeated int64 marketer_ids = 9;
  repeated int64 analytic_account_ids = 10;
  repeated int64 adaccount_ids = 11;
}

message ListCampaignsResponse {
  repeated exmsg.models.Campaign campaigns = 1;
  int64 total = 2;
  int64 count_not_in_biz = 3;
}

message BulkUpdateCampaignsRequest {
  int64 company_id = 1;
  int64 marketer_id = 2;
  int64 updated_by_id = 3;
  repeated int64 campaign_ids = 4;
  string landing = 5;
}

message BulkUpdateCampaignsResponse {
  int64 updated_by_id = 1;
  google.protobuf.Timestamp updated_at = 2;
}

message AssignCompanyForCampaignRequest {
  int64 company_id = 1;
  int64 updated_by_id = 2;
  int64 campaign_id = 3;
  bool disable = 4;
}

message AssignCompanyForCampaignResponse {
  int64 updated_by_id = 1;
  google.protobuf.Timestamp updated_at = 2;
}

message BulkDeleteCampaignsRequest {
  int64 company_id = 1;
  repeated int64 ids = 2;
}

message ListMarketersRequest {
  int64 company_id = 1;
  string sort_field = 2;
  bool is_asc = 3;
}

message ListMarketersResponse {
  repeated exmsg.models.Marketer marketers = 1;
  int64 total = 2;
}

message BatchInsertAdAccountsRequest {
  repeated exmsg.models.AdAccount adaccounts = 1;
}

message BatchInsertAdAccountsResponse {
  bool success = 1;
}

message BatchInsertCampaignsRequest {
  repeated exmsg.models.Campaign campaigns = 1;
}

message BatchInsertCampaignsResponse {
  bool success = 1; 
} 

message BatchInsertAdsetsRequest {
  repeated exmsg.models.Adset adsets = 1;
}

message BatchInsertAdsetsResponse {
  bool success = 1;
}

message BatchInsertAdsRequest {
  repeated exmsg.models.Ad ads = 1;
}

message BatchInsertAdsResponse {
  bool success = 1;
}

message BatchInsertSpendingsRequest {
  repeated exmsg.models.Spending spendings = 1;
}

message BatchInsertSpendingsResponse {
  bool success = 1;
}

message GetTokenByAdIDRequest {
  int64 ad_id = 1;
}

message GetTokenByAdIDResponse {
  string token = 1;
}