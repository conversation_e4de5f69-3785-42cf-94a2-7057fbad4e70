package repository

import (
	"context"
	"database/sql"
	"errors"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.SpendingRepository = (*PostgresSpendingRepository)(nil)

type PostgresSpendingRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresSpendingRepository(db *sqlx.DB) *PostgresSpendingRepository {
	return &PostgresSpendingRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

const (
	spendingsTable = "fb.spendings"
)

var (
	insertedSpendingColumns = []string{
		"ad_id",
		"campaign_id",
		"adset_id",
		"adaccount_id",
		"analytic_account_id",
		"date_start",
		"hourly_stat",
		"raw_numeric",
		"cpm",
		"cpc",
		"ctr",
		"clicks",
		"frequency",
		"impressions",
		"p25",
		"p50",
		"p75",
		"p95",
		"video_average",
		"exchange_rate",
	}
)

func (ps *PostgresSpendingRepository) BatchInsert(ctx context.Context, spendings []domain.Spending) error {
	subFed := ps.queryBuilder.Select("currency", "company_id").
		From(adaccountsTable).
		Where(squirrel.Eq{"id": spendings[0].AdAccountID}).
		Limit(1)
	subRate := ps.queryBuilder.Select(`
		COALESCE(
			CASE
				WHEN fed.currency = 'VND' THEN 1
				ELSE cr.rate
			END,
			1
		) AS rate
	`).
		FromSelect(subFed, "fed").
		LeftJoin(
			`marketing.currencies AS cr 
			ON cr.company_id = fed.company_id 
			AND cr.currency = fed.currency`,
		)
	rateQuery, args, err := subRate.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to build rate query: %s", err.Error())
		return err
	}
	var rate float64
	if err := ps.db.GetContext(ctx, &rate, rateQuery, args...); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			rate = 1.0 // default to 1 (as VND)
		} else {
			logger.AthenaLogger.Errorf("failed to get rate: %s", err.Error())
			return err
		}
	}

	qb := ps.queryBuilder.Insert(spendingsTable).Columns(insertedSpendingColumns...).
		SuffixExpr(squirrel.Expr(`
			ON CONFLICT (ad_id, date_start, hourly_stat) DO UPDATE SET
				raw_numeric = EXCLUDED.raw_numeric,
				cpm = EXCLUDED.cpm,
				cpc = EXCLUDED.cpc,
				ctr = EXCLUDED.ctr,
				clicks = EXCLUDED.clicks,
				frequency = EXCLUDED.frequency,
				p25 = EXCLUDED.p25,
				p50 = EXCLUDED.p50,
				p75 = EXCLUDED.p75,
				p95 = EXCLUDED.p95,
				video_average = EXCLUDED.video_average,
				impressions = EXCLUDED.impressions,
				campaign_id = EXCLUDED.campaign_id,
				adset_id = EXCLUDED.adset_id,
				adaccount_id = EXCLUDED.adaccount_id,
				analytic_account_id = EXCLUDED.analytic_account_id,
				updated_at = NOW(),
				deleted_at = NULL
		`))
	for _, spending := range spendings {
		qb = qb.Values(
			spending.AdID,
			spending.CampaignID,
			spending.AdsetID,
			spending.AdAccountID,
			spending.AnalyticAccountID,
			spending.DateStart,
			spending.HourlyStat,
			spending.RawNumeric,
			spending.CPM,
			spending.CPC,
			spending.CTR,
			spending.Clicks,
			spending.Frequency,
			spending.Impressions,
			spending.P25,
			spending.P50,
			spending.P75,
			spending.P95,
			spending.VideoAverage,
			rate,
		)
	}
	insertQuery, args, err := qb.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorf("failed to build insert query: %v", err)
		return err
	}
	if _, err = ps.db.ExecContext(ctx, insertQuery, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to execute insert query: %v", err)
		return err
	}

	return nil
}

func (ps *PostgresSpendingRepository) UpdateMarketerID(ctx context.Context) error {
	cte := `mat AS (
			SELECT ad.id, camp.marketer_id
			FROM fb.campaigns camp
			JOIN fb.adsets adset ON camp.id = adset.campaign_id
			JOIN fb.ads ad ON adset.id = ad.adset_id
			WHERE camp.deleted_at IS NULL)`

	updateBuilder := ps.queryBuilder.Update("fb.spendings").
		Set("marketer_id", squirrel.Expr("mat.marketer_id")).
		From("mat").
		Where("fb.spendings.ad_id = mat.id").
		Where("fb.spendings.marketer_id IS NULL").
		Prefix("WITH " + cte)

	query, args, err := updateBuilder.ToSql()
	if err != nil {
		return err
	}

	_, err = ps.db.ExecContext(ctx, query, args...)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to update marketer_id: %v", err)
		return err
	}

	return nil
}
