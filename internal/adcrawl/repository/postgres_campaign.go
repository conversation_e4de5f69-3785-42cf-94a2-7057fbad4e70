package repository

import (
	"context"
	"errors"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

var _ domain.CampaignRepository = (*PostgresCampaignRepository)(nil)

type PostgresCampaignRepository struct {
	db           *sqlx.DB
	queryBuilder squirrel.StatementBuilderType
}

func NewPostgresCampaignRepository(db *sqlx.DB) *PostgresCampaignRepository {
	return &PostgresCampaignRepository{
		db:           db,
		queryBuilder: squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar),
	}
}

const (
	campaignsTable = "fb.campaigns"
)

var (
	insertedCampaignColumns = []string{
		"id",
		"company_id",
		"name",
		"status",
		"effective_status",
		"adaccount_id",
		"objective",
		"marketer_id",
		"updated_by_id",
	}
)

func (pcr *PostgresCampaignRepository) BatchInsert(ctx context.Context, campaigns []domain.Campaign, scheduled bool) error {
	qb := pcr.queryBuilder.Insert(campaignsTable).Columns(insertedCampaignColumns...)
	if scheduled {
		qb = qb.SuffixExpr(squirrel.Expr(`
			ON CONFLICT (company_id, id) DO UPDATE SET
				name = EXCLUDED.name,
				status = EXCLUDED.status,
				effective_status = EXCLUDED.effective_status,
				adaccount_id = EXCLUDED.adaccount_id,
				objective = EXCLUDED.objective,
				updated_at = NOW()
			WHERE fb.campaigns.deleted_at IS NULL
		`))
	} else {
		qb = qb.SuffixExpr(squirrel.Expr(`
			ON CONFLICT (company_id, id) DO UPDATE SET
				name = EXCLUDED.name,
				status = EXCLUDED.status,
				effective_status = EXCLUDED.effective_status,
				adaccount_id = EXCLUDED.adaccount_id,
				objective = EXCLUDED.objective,
				updated_at = NOW(),
				deleted_at = NULL
		`))
	}

	for _, campaign := range campaigns {
		qb = qb.Values(
			campaign.ID,
			campaign.CompanyID,
			campaign.Name,
			campaign.Status,
			campaign.EffectiveStatus,
			campaign.AdAccountID,
			campaign.Objective,
			campaign.MarketerID,
			campaign.MarketerID,
		)
	}
	insertQuery, args, err := qb.ToSql()
	if err != nil {
		logger.AthenaLogger.Errorw("failed to build query", "error", err)
		return err
	}
	_, err = pcr.db.ExecContext(ctx, insertQuery, args...)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to insert campaigns: %v", err)
		return err
	}

	return nil
}

func (pcr *PostgresCampaignRepository) BulkUpdate(ctx context.Context, campaignIDs []int64, companyID int64, updateFields []string, updateValues []interface{}) (time.Time, error) {
	q := pcr.queryBuilder.Update("fb.campaigns").
		Set("updated_at", squirrel.Expr("NOW()")).
		Where(squirrel.And{
			squirrel.Eq{"company_id": companyID},
			squirrel.Expr("id = ANY(?)", pq.Array(campaignIDs)),
			squirrel.Eq{"deleted_at": nil},
		})
	if len(updateFields) != len(updateValues) {
		return time.Time{}, errors.New("updateFields and updateValues length mismatch")
	}
	for i, field := range updateFields {
		q = q.Set(field, updateValues[i])
	}

	stmt, args, err := q.ToSql()
	if err != nil {
		return time.Time{}, err
	}

	if _, err = pcr.db.ExecContext(ctx, stmt, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to update marketer (assign) for campaigns: %v", err)
		return time.Time{}, err
	}

	return time.Now(), nil
}

func (pcr *PostgresCampaignRepository) ToggleRunOnThisOrg(ctx context.Context, campaignIDs []int64, runOnThisOrg bool, updatedByID int64) error {
	q := pcr.queryBuilder.Update("fb.campaigns").
		Set("available", runOnThisOrg).
		Set("updated_by_id", updatedByID).
		Set("updated_at", squirrel.Expr("NOW()")).
		Where(squirrel.And{
			squirrel.Eq{"id": campaignIDs},
			squirrel.Eq{"deleted_at": nil},
		})
	stmt, args, err := q.ToSql()
	if err != nil {
		return err
	}
	if _, err = pcr.db.ExecContext(ctx, stmt, args...); err != nil {
		logger.AthenaLogger.Errorf("failed to update company_id (assign) for campaigns: %v", err)
		return err
	}
	return nil
}

func (pcr *PostgresCampaignRepository) BulkUpdateCountryID(ctx context.Context) error {
	cte := `mat AS (
			SELECT camp.id, camp.company_id, aa.country_id
			FROM fb.campaigns camp
			JOIN fb.adaccounts aa ON aa.id = camp.adaccount_id
				AND aa.company_id = camp.company_id
			WHERE camp.deleted_at IS NULL 
			AND camp.country_id IS NULL
			AND aa.country_id IS NOT NULL)`

	updateBuilder := pcr.queryBuilder.Update(campaignsTable).
		Set("country_id", squirrel.Expr("mat.country_id")).
		From("mat").
		Where(squirrel.And{
			squirrel.Expr("fb.campaigns.company_id = mat.company_id"),
			squirrel.Expr("fb.campaigns.id = mat.id"),
		}).
		Prefix("WITH " + cte)

	query, args, err := updateBuilder.ToSql()
	if err != nil {
		return err
	}

	_, err = pcr.db.ExecContext(ctx, query, args...)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to update country id for campaigns: %v", err)
		return err
	}

	return nil
}
