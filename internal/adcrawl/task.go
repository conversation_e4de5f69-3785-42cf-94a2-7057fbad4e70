package adcrawl

import (
	"encoding/json"

	"github.com/hibiken/asynq"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

type TaskType string

const (
	TypeFetchAdAccountIDs TaskType = "fetch:adaccount_ids"
	TypeFetchAdAccounts   TaskType = "fetch:adccounts"
	TypeFetchCampaigns    TaskType = "fetch:campaigns"
	TypeFetchAdSets       TaskType = "fetch:adsets"
	TypeFetchAds          TaskType = "fetch:ads"
	TypeFetchSpendings    TaskType = "fetch:spendings"

	TypeInsertAdAccounts TaskType = "insert:adaccounts"
	TypeInsertCampaigns  TaskType = "insert:campaigns"
	TypeInsertAdSets     TaskType = "insert:adsets"
	TypeInsertAds        TaskType = "insert:ads"
	TypeInsertSpendings  TaskType = "insert:spendings"

	TypeSeedFetchSpendings TaskType = "seed:fetch_spendings"
	TypeUpdateMarketerID   TaskType = "update:marketer_id"
	TypeUpdateCountryID    TaskType = "update:country_id"
)

type FetchAdRelatedObjectsPayload struct {
	URL         string
	AccessToken string
	ExtraParams map[string]interface{}
}

func NewFetchAdRelatedObjectsTask(taskType string, url string, token string, extra map[string]interface{}) (*asynq.Task, error) {
	payload, err := json.Marshal(FetchAdRelatedObjectsPayload{URL: url, AccessToken: token, ExtraParams: extra})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(taskType), payload), nil
}

type InsertAdAccountsPayload struct {
	AdAccounts  []domain.AdAccount
	AccessToken string
	Scheduled   bool
	Critical    bool
	ReAdd       bool
}

func NewInsertAdAccountsTask(adAccounts []domain.AdAccount, token string, scheduled bool, critical bool, reAdd bool) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertAdAccountsPayload{
		AdAccounts:  adAccounts,
		AccessToken: token,
		Scheduled:   scheduled,
		Critical:    critical,
		ReAdd:       reAdd,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertAdAccounts), payload), nil
}

type InsertCampaignsPayload struct {
	Campaigns   []domain.Campaign
	AccessToken string
	Scheduled   bool
	Critical    bool
}

func NewInsertCampaignsTask(campaigns []domain.Campaign, token string, scheduled bool, critical bool) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertCampaignsPayload{
		Campaigns:   campaigns,
		AccessToken: token,
		Scheduled:   scheduled,
		Critical:    critical,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertCampaigns), payload), nil
}

type InsertAdSetsPayload struct {
	AdSets      []domain.AdSet
	AccessToken string
	Scheduled   bool
	Critical    bool
}

func NewInsertAdSetsTask(adSets []domain.AdSet, token string, scheduled bool, critical bool) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertAdSetsPayload{
		AdSets:      adSets,
		AccessToken: token,
		Scheduled:   scheduled,
		Critical:    critical,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertAdSets), payload), nil
}

type InsertAdsPayload struct {
	Ads         []domain.Ad
	AccessToken string
	Scheduled   bool
	Critical    bool
}

func NewInsertAdsTask(ads []domain.Ad, token string, scheduled bool, critical bool) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertAdsPayload{
		Ads:         ads,
		AccessToken: token,
		Scheduled:   scheduled,
		Critical:    critical,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertAds), payload), nil
}

type InsertSpendingsPayload struct {
	Spendings []domain.Spending
	Scheduled bool
}

func NewInsertSpendingsTask(spendings []domain.Spending, scheduled bool) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertSpendingsPayload{
		Spendings: spendings,
		Scheduled: scheduled,
	})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertSpendings), payload), nil
}

type SeedFetchSpendingsPayload struct {
}

func NewSeedFetchSpendingsTask() (*asynq.Task, error) {
	payload, err := json.Marshal(SeedFetchSpendingsPayload{})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeSeedFetchSpendings), payload), nil
}

// Update marketer_id field in table fb.spendings (scheduled task)
// marketer_id of spendings is equal to marketer_id of campaign that contains the spending
type UpdateMarketerIDPayload struct {
}

func NewUpdateMarketerIDTask() (*asynq.Task, error) {
	payload, err := json.Marshal(UpdateMarketerIDPayload{})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeUpdateMarketerID), payload), nil
}

// Update country_id field in table fb.campaigns (scheduled task)
// country_id of campaigns is equal to country_id of adaccount that campaign belongs to
type UpdateCountryIDPayload struct {
}

func NewUpdateCountryIDTask() (*asynq.Task, error) {
	payload, err := json.Marshal(UpdateCountryIDPayload{})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeUpdateCountryID), payload), nil
}
