package adcrawl

// This file contains all function used for fetching object's informations from Facebook Graph API

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	neturl "net/url"
	"strings"
	"time"

	http2 "gitlab.com/a7923/athena-go/pkg/http"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"

	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

const (
	DefaultBaseURL string = "https://graph.facebook.com/v22.0"
)

func loadBaseURL() string {
	baseURL = viper.GetString("crawl.base_url")
	if baseURL == "" {
		return DefaultBaseURL
	}
	updateEndpoints()
	return baseURL
}

func updateEndpoints() {
	validateTokenEndpoint = baseURL + "/debug_token?input_token=%s&access_token=%s"
	fetchAnalyticAccountEndpoint = baseURL + "/me?fields=id,name,picture&access_token=%s"
	fetchAdAccountsEndpoint = baseURL + "/me/adaccounts?fields=account_id,name,account_status,currency,business_country_code,timezone_offset_hours_utc&limit=100&access_token=%s"
	fetchCampaignsEndpoint = baseURL + "/act_%d?fields=campaigns{id,effective_status,status,name,account_id,objective}&limit=100&access_token=%s"
	fetchAdSetsEndpoint = baseURL + "/%d?fields=adsets{id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id,targeting{geo_locations{countries}}}&limit=25&access_token=%s"
	fetchAdsEndpoint = baseURL + "/%d?fields=ads{id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id,effective_object_story_id},adset_id}&limit=5&access_token=%s"
	fetchSpendingsEndpoint = baseURL + "/%d/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,campaign_id,adset_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=25&access_token=%s&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	exchangeTokenEndpoint = baseURL + "/oauth/access_token?grant_type=fb_exchange_token&client_id=%s&client_secret=%s&fb_exchange_token=%s"

	fetchCampaignsEndpointV2 = baseURL + "/act_%d/campaigns?fields=id,effective_status,status,name,account_id,objective&access_token=%s&filtering=[{'field':'campaign.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdSetsEndpointV2 = baseURL + "/act_%d/adsets?fields=id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id,targeting{geo_locations{countries}}&access_token=%s&filtering=[{'field':'adset.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdsEndpointV2 = baseURL + "/act_%d/ads?fields=id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id,effective_object_story_id},adset_id&access_token=%s&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchSpendingsFromAdAccountEndpoint = baseURL + "/act_%d/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,campaign_id,adset_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=30&access_token=%s&level=ad&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]&sort=ad_id_ascending"
}

var (
	baseURL                      = DefaultBaseURL
	validateTokenEndpoint        = baseURL + "/debug_token?input_token=%s&access_token=%s"
	fetchAnalyticAccountEndpoint = baseURL + "/me?fields=id,name,picture&access_token=%s"
	fetchAdAccountsEndpoint      = baseURL + "/me/adaccounts?fields=account_id,name,account_status,currency,business_country_code,timezone_offset_hours_utc&limit=100&access_token=%s"
	fetchCampaignsEndpoint       = baseURL + "/act_%d?fields=campaigns{id,effective_status,status,name,account_id,objective}&limit=100&access_token=%s&filtering=[{'field':'campaign.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdSetsEndpoint          = baseURL + "/%d?fields=adsets{id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id,targeting{geo_locations{countries}}}&limit=25&access_token=%s&filtering=[{'field':'adset.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdsEndpoint             = baseURL + "/%d?fields=ads{id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id,effective_object_story_id},adset_id}&limit=5&access_token=%s&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchSpendingsEndpoint       = baseURL + "/%d/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,campaign_id,adset_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=25&access_token=%s&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	exchangeTokenEndpoint        = baseURL + "/oauth/access_token?grant_type=fb_exchange_token&client_id=%s&client_secret=%s&fb_exchange_token=%s"

	// version 2 of crawling enable asynchronous crawling without worrying about foreign key constraints between objects
	fetchCampaignsEndpointV2            = baseURL + "/act_%d/campaigns?fields=id,effective_status,status,name,account_id,objective&access_token=%s&filtering=[{'field':'campaign.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdSetsEndpointV2               = baseURL + "/act_%d/adsets?fields=id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id,targeting{geo_locations{countries}}&access_token=%s&filtering=[{'field':'adset.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchAdsEndpointV2                  = baseURL + "/act_%d/ads?fields=id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id,effective_object_story_id},adset_id&access_token=%s&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]"
	fetchSpendingsFromAdAccountEndpoint = baseURL + "/act_%d/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,campaign_id,adset_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=30&access_token=%s&level=ad&filtering=[{'field':'ad.effective_status','operator':'IN','value':['ACTIVE','PAUSED','DELETED','PENDING_REVIEW','DISAPPROVED','PREAPPROVED','PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ARCHIVED','ADSET_PAUSED','IN_PROCESS','WITH_ISSUES']}]&sort=ad_id_ascending"
	fetchSpendingsAsyncJobEndpoint      = baseURL + "/%v/insights?limit=500&access_token=%s"
	fetchAsyncJobStatusEndpoint         = baseURL + "/%v?access_token=%s"
)

type Fetcher struct {
	httpClient *http.Client
}

type FetcherConfig struct {
	RequestTimeout      time.Duration
	MaxIdleConnsPerHost int
}

const (
	DefaultRequestTimeout      = 35 * time.Second
	DefaultMaxIdleConnsPerHost = 25
)

func NewFetcher(cfg FetcherConfig) *Fetcher {
	baseURL = loadBaseURL()
	if cfg.MaxIdleConnsPerHost == 0 {
		cfg.MaxIdleConnsPerHost = DefaultMaxIdleConnsPerHost
	}
	if cfg.RequestTimeout == time.Duration(0) {
		cfg.RequestTimeout = DefaultRequestTimeout
	}

	transport := &http.Transport{
		MaxIdleConnsPerHost: cfg.MaxIdleConnsPerHost,
	}
	return &Fetcher{
		httpClient: &http.Client{
			Transport: transport,
			Timeout:   cfg.RequestTimeout,
		},
	}
}
func (f *Fetcher) ExchangeToken(token string) (string, error) {
	url := fmt.Sprintf(exchangeTokenEndpoint, viper.GetString("fb.client_id"), viper.GetString("fb.client_secret"), token)
	resp := map[string]interface{}{}
	err := http2.NewRequest(&http2.RequestOptions{
		Method: http.MethodGet,
		URL:    url,
	}, &resp)

	if err != nil {
		return "", err
	}

	return cast.ToString(resp["access_token"]), nil
}

func (f *Fetcher) ValidateToken(token string) (bool, error) {
	url := fmt.Sprintf(validateTokenEndpoint, token, token)
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}

	if resp.StatusCode != 200 {
		errMsg := gjson.GetBytes(body, "error.message").String()
		return false, fmt.Errorf("failed to validate token: %s", errMsg)
	}

	return gjson.GetBytes(body, "data.is_valid").Bool(), nil
}

func (f *Fetcher) FetchAnalyticAccount(token string) (*domain.AnalyticAccount, error) {
	url := fmt.Sprintf(fetchAnalyticAccountEndpoint, token)
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("failed to fetch analytic account: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	acc := domain.AnalyticAccount{
		Username:      gjson.GetBytes(body, "name").String(),
		ID:            cast.ToInt64(gjson.GetBytes(body, "id").String()),
		ProfilePicURL: gjson.GetBytes(body, "picture.data.url").String(),
	}

	return &acc, nil
}

type AdAccountIDFetchingResponse struct {
	adAccountIDs []string
	nextPage     string
}

func (f *Fetcher) FetchAdAccountIDs(url string) (*AdAccountIDFetchingResponse, error) {
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var adAccountIds []string
	results := gjson.GetBytes(body, "data.#.account_id")
	results.ForEach(func(_, value gjson.Result) bool {
		adAccountIds = append(adAccountIds, value.String())
		return true
	})

	nextPage := gjson.GetBytes(body, "paging.next").String()

	return &AdAccountIDFetchingResponse{
		adAccountIDs: adAccountIds,
		nextPage:     nextPage,
	}, nil
}

type AdAccountFetchingResponse struct {
	adAccountInfos []domain.AdAccount
	nextPage       string
}

func (f *Fetcher) FetchAdAccounts(url string) (*AdAccountFetchingResponse, error) {
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	var adAccInfos []domain.AdAccount
	adAccJSON := gjson.GetBytes(body, "data").String()
	if resp.StatusCode == 200 && len(adAccJSON) == 0 {
		return &AdAccountFetchingResponse{
			adAccountInfos: adAccInfos,
		}, nil
	}

	var dummyAdAccInfos []domain.DummyAdAccount
	if err := json.Unmarshal([]byte(adAccJSON), &dummyAdAccInfos); err != nil {
		return nil, err
	}

	for i := range dummyAdAccInfos {
		adAccInfos = append(adAccInfos, dummyAdAccInfos[i].ToAdAccount())
	}

	nextPage := gjson.GetBytes(body, "paging.next").String()

	return &AdAccountFetchingResponse{
		adAccountInfos: adAccInfos,
		nextPage:       nextPage,
	}, nil
}

type CampaignFetchingResponse struct {
	campaignInfos []domain.Campaign
	nextPages     []string
}

func (f *Fetcher) FetchCampaigns(url string) (*CampaignFetchingResponse, error) {
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	campaignInfos := make([]domain.Campaign, 0)
	nextPages := make([]string, 0)
	if gjson.GetBytes(body, "data").IsArray() {
		campaignJSON := gjson.GetBytes(body, "data").String()
		if campaignJSON != "" {
			var dummyCampaignInfos []domain.DummyCampaign
			if err := json.Unmarshal([]byte(campaignJSON), &dummyCampaignInfos); err != nil {
				logger.AthenaLogger.Errorf("failed to unmarshal campaign json: %s", err.Error())
			}
			for i := range dummyCampaignInfos {
				campaignInfos = append(campaignInfos, dummyCampaignInfos[i].ToCampaign())
			}
			nextPage := gjson.GetBytes(body, "paging.next").String()
			if nextPage != "" {
				nextPages = append(nextPages, nextPage)
			}
		}
	} else {
		campaignJSON := gjson.GetBytes(body, "campaigns.data").String()
		if resp.StatusCode == 200 && len(campaignJSON) == 0 {
			return &CampaignFetchingResponse{
				campaignInfos: campaignInfos,
			}, nil
		}
		var dummyCampaignInfos []domain.DummyCampaign
		if err := json.Unmarshal([]byte(campaignJSON), &dummyCampaignInfos); err != nil {
			return nil, err
		}

		for i := range dummyCampaignInfos {
			campaignInfos = append(campaignInfos, dummyCampaignInfos[i].ToCampaign())
		}
		nextPage := gjson.GetBytes(body, "campaigns.paging.next").String()
		if nextPage != "" {
			nextPages = append(nextPages, nextPage)
		}
	}

	nextPage := gjson.GetBytes(body, "data.paging.next").String()
	if nextPage != "" {
		nextPages = append(nextPages, nextPage)
	}
	return &CampaignFetchingResponse{
		campaignInfos: campaignInfos,
		nextPages:     nextPages,
	}, nil
}

type AdSetFetchingResponse struct {
	adsetInfos []domain.AdSet
	nextPages  []string
}

func (f *Fetcher) FetchAdSets(url string) (*AdSetFetchingResponse, error) {
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	adsetInfos := make([]domain.AdSet, 0)
	nextPages := make([]string, 0)

	if gjson.GetBytes(body, "data").IsArray() {
		adsetJSON := gjson.GetBytes(body, "data").String()
		if adsetJSON != "" {
			var dummyAdsetInfos []domain.DummyAdSet
			if err := json.Unmarshal([]byte(adsetJSON), &dummyAdsetInfos); err != nil {
				logger.AthenaLogger.Errorf("failed to unmarshal adset json: %s", err.Error())
			} else {
				for i := range dummyAdsetInfos {
					adsetInfos = append(adsetInfos, dummyAdsetInfos[i].ToAdSet())
				}
				nextPage := gjson.GetBytes(body, "paging.next").String()
				if nextPage != "" {
					nextPages = append(nextPages, nextPage)
				}
			}
		}
	} else {
		adsetJSON := gjson.GetBytes(body, "adsets.data").String()
		if resp.StatusCode == 200 && len(adsetJSON) == 0 {
			return &AdSetFetchingResponse{
				adsetInfos: adsetInfos,
			}, nil
		}

		var dummyAdsetInfos []domain.DummyAdSet
		if err := json.Unmarshal([]byte(adsetJSON), &dummyAdsetInfos); err != nil {
			return nil, err
		}

		for i := range dummyAdsetInfos {
			adsetInfos = append(adsetInfos, dummyAdsetInfos[i].ToAdSet())
		}

		nextPage := gjson.GetBytes(body, "adsets.paging.next").String()
		if nextPage != "" {
			nextPages = append(nextPages, nextPage)
		}
	}

	nextPage := gjson.GetBytes(body, "data.paging.next").String()
	if nextPage != "" {
		nextPages = append(nextPages, nextPage)
	}

	return &AdSetFetchingResponse{
		adsetInfos: adsetInfos,
		nextPages:  nextPages,
	}, nil
}

type AdFetchingResponse struct {
	adInfos   []domain.Ad
	nextPages []string
}

func (f *Fetcher) FetchAds(url string) (*AdFetchingResponse, error) {
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	adInfos := make([]domain.Ad, 0)
	nextPages := make([]string, 0)

	if gjson.GetBytes(body, "data").IsArray() {
		adJSON := gjson.GetBytes(body, "data").String()
		if adJSON != "" {
			var dummyAdInfos []domain.DummyAd
			if err := json.Unmarshal([]byte(adJSON), &dummyAdInfos); err != nil {
				logger.AthenaLogger.Errorf("failed to unmarshal ad json: %s", err.Error())
			} else {
				for i := range dummyAdInfos {
					adInfos = append(adInfos, dummyAdInfos[i].ToAd())
				}

				// Parse CreatedAt
				createdTime := gjson.GetBytes(body, "data.#.created_time")
				currentID := len(adInfos) - len(dummyAdInfos)
				createdTime.ForEach(func(_, value gjson.Result) bool {
					parsed, err := parseTime(value.String())
					if err != nil {
						logger.AthenaLogger.Errorw("failed to parse created time", "error", err.Error(), "payload", value.String())
						return true
					}
					adInfos[currentID].CreatedAt = parsed
					currentID++
					return true
				})

				// Parse creative fields
				currentID = len(adInfos) - len(dummyAdInfos)
				gjson.GetBytes(body, "data").ForEach(func(_, value gjson.Result) bool {
					pageID := value.Get("adcreatives.data.0.object_story_spec.page_id").String()
					if pageID != "" {
						adInfos[currentID].PageID = pageID
					}

					objectStoryID := value.Get("adcreatives.data.0.object_story_id").String()
					if objectStoryID != "" {
						adInfos[currentID].ObjectStoryID = &objectStoryID
						if pageID == "" {
							adInfos[currentID].PageID = strings.Split(objectStoryID, "_")[0]
						}
					}

					effectiveObjectStoryID := value.Get("adcreatives.data.0.effective_object_story_id").String()
					adInfos[currentID].PostID = nil
					if len(effectiveObjectStoryID) > 0 {
						adInfos[currentID].PostID = &effectiveObjectStoryID
					}

					objectStorySpec := value.Get("adcreatives.data.0.object_story_spec").String()
					if objectStorySpec != "" {
						adInfos[currentID].ObjectStorySpec = []byte(objectStorySpec)
					} else {
						adInfos[currentID].ObjectStorySpec = nil
					}

					imageURL := value.Get("adcreatives.data.0.object_story_spec.video_data.image_url").String()
					if imageURL != "" {
						adInfos[currentID].ImageURL = &imageURL
					}
					currentID++
					return true
				})

				nextPage := gjson.GetBytes(body, "paging.next").String()
				if nextPage != "" {
					nextPages = append(nextPages, nextPage)
				}
			}
		}
	} else {
		adJSON := gjson.GetBytes(body, "ads.data").String()
		if resp.StatusCode == 200 && len(adJSON) == 0 {
			return &AdFetchingResponse{
				adInfos: adInfos,
			}, nil
		}

		var dummyAdInfos []domain.DummyAd
		if err := json.Unmarshal([]byte(adJSON), &dummyAdInfos); err != nil {
			return nil, err
		}
		for i := range dummyAdInfos {
			adInfos = append(adInfos, dummyAdInfos[i].ToAd())
		}

		// Parse CreatedAt
		createdTime := gjson.GetBytes(body, "ads.data.#.created_time")
		currentID := 0
		createdTime.ForEach(func(_, value gjson.Result) bool {
			parsed, err := parseTime(value.String())
			if err != nil {
				logger.AthenaLogger.Errorw("failed to parse created time", "error", err.Error(), "payload", value.String())
				return true
			}
			adInfos[currentID].CreatedAt = parsed
			currentID++
			return true
		})

		// Parse creative fields
		currentID = 0
		gjson.GetBytes(body, "ads.data").ForEach(func(_, value gjson.Result) bool {
			pageID := value.Get("adcreatives.data.0.object_story_spec.page_id").String()
			if pageID != "" {
				adInfos[currentID].PageID = pageID
			}

			objectStoryID := value.Get("adcreatives.data.0.object_story_id").String()
			if objectStoryID != "" {
				adInfos[currentID].ObjectStoryID = &objectStoryID
				if pageID == "" {
					adInfos[currentID].PageID = strings.Split(objectStoryID, "_")[0]
				}
			}

			effectiveObjectStoryID := value.Get("adcreatives.data.0.effective_object_story_id").String()
			adInfos[currentID].PostID = nil
			if len(effectiveObjectStoryID) > 0 {
				adInfos[currentID].PostID = &effectiveObjectStoryID
			}

			objectStorySpec := value.Get("adcreatives.data.0.object_story_spec").String()
			if objectStorySpec != "" {
				adInfos[currentID].ObjectStorySpec = []byte(objectStorySpec)
			} else {
				adInfos[currentID].ObjectStorySpec = nil
			}

			imageURL := value.Get("adcreatives.data.0.object_story_spec.video_data.image_url").String()
			if imageURL != "" {
				adInfos[currentID].ImageURL = &imageURL
			}

			currentID++
			return true
		})

		if currentID != len(adInfos) {
			logger.AthenaLogger.Warn("Seem like there're something wrong inside parsing JSON data: currentID != len(adInfos)")
		}

		nextPage := gjson.GetBytes(body, "ads.paging.next").String()
		if nextPage != "" {
			nextPages = append(nextPages, nextPage)
		}
	}

	topNextPage := gjson.GetBytes(body, "data.paging.next").String()
	if topNextPage != "" {
		nextPages = append(nextPages, topNextPage)
	}

	return &AdFetchingResponse{
		adInfos:   adInfos,
		nextPages: nextPages,
	}, nil
}

type SpendingFetchingResponse struct {
	spInfos   []domain.Spending
	nextPages []string
}

type AsyncJobResponse struct {
	ReportRunId string `json:"report_run_id"`
}

type AsyncJobStatus struct {
	Id                     string `json:"id"`
	AccountId              string `json:"account_id"`
	TimeRef                int    `json:"time_ref"`
	AsyncStatus            string `json:"async_status"`
	AsyncPercentCompletion int    `json:"async_percent_completion"`
	IsRunning              bool   `json:"is_running"`
	DateStart              string `json:"date_start"`
	DateStop               string `json:"date_stop"`
}

// CreateAsyncJob creates a new async job for fetching spendings and returns job id
func (f *Fetcher) CreateAsyncJob(url string, token string) (string, error) {
	createJobResp := AsyncJobResponse{}
	err := http2.NewRequest(&http2.RequestOptions{
		Method: http.MethodPost,
		URL:    url,
	}, &createJobResp)
	if err != nil {
		return "", err
	}

	return createJobResp.ReportRunId, nil
}

func (f *Fetcher) CheckAsyncJobStatus(url string) (*AsyncJobStatus, error) {
	status := AsyncJobStatus{}
	err := http2.NewRequest(&http2.RequestOptions{
		Method: http.MethodGet,
		URL:    url,
	}, &status)
	if err != nil {
		return nil, err
	}

	return &status, nil
}

func (f *Fetcher) FetchSpendings(payload FetchAdRelatedObjectsPayload) (*SpendingFetchingResponse, error) {
	url := payload.URL

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	code := extractErrorCode(body)
	switch code {
	case InvalidTokenCode:
		return nil, ErrInvalidToken
	case TooManyRequestCode:
		return nil, ErrAPIRateLimit
	default:
		if code > 0 {
			logger.AthenaLogger.Debugw("facebook's response return with error",
				"code", code,
				"url", url,
				"body", string(body),
			)
			return nil, err
		}
	}

	if viper.GetBool("logging.enable") && resp.StatusCode != 200 {
		logger.AthenaLogger.Warnw("Failed to fetch spendings",
			"status_code", resp.StatusCode,
			"body", string(body),
			"extra", utils.ToJSONString(payload.ExtraParams),
		)
	}

	spInfos := make([]domain.Spending, 0)
	dataJSON := gjson.GetBytes(body, "data.#.insights")
	if resp.StatusCode == 200 && len(dataJSON.Raw) == 0 {
		return &SpendingFetchingResponse{
			spInfos:   spInfos,
			nextPages: make([]string, 0),
		}, nil
	}

	nextPages := make([]string, 0)
	if dataJSON.String() != "[]" {
		dataJSON.ForEach(func(key, value gjson.Result) bool {
			data := value.String()
			if data == "" {
				return true
			}

			spJSON := gjson.Get(data, "data")
			spJSON.ForEach(func(key, value gjson.Result) bool {
				var dummySp domain.DummySpending
				p := value.String()
				if p == "" {
					return true
				}
				if err := json.Unmarshal([]byte(p), &dummySp); err != nil {
					log.Printf("failed to unmarshal spending data: %v", err)
				} else {
					sp := domain.ConvertDummySpendingToSpending(dummySp)
					if sp != nil {
						spInfos = append(spInfos, *sp)
						return true
					}
					logger.AthenaLogger.Warnw("Failed to convert dummy spending to spending", "payload", p)
				}
				return true
			})
			nextPage := gjson.Get(data, "paging.next").String()
			if nextPage != "" {
				nextPages = append(nextPages, nextPage)
			}
			return true
		})

		adAccountID := gjson.GetBytes(body, "id").String()
		adAccountID = strings.TrimPrefix(adAccountID, "act_")
		for i := range spInfos {
			spInfos[i].AdAccountID = cast.ToInt64(adAccountID)
		}
	} else {
		spJSON := gjson.GetBytes(body, "data")
		spJSON.ForEach(func(key, value gjson.Result) bool {
			dummySp := domain.DummySpending{}
			p := value.String()
			if p == "" {
				return true
			}
			if err := json.Unmarshal([]byte(p), &dummySp); err != nil {
				log.Printf("failed to unmarshal spending data: %v", err)
			} else {
				sp := domain.ConvertDummySpendingToSpending(dummySp)
				if sp != nil {
					spInfos = append(spInfos, *sp)
					return true
				}
				logger.AthenaLogger.Warnw("Failed to convert dummy spending to spending", "payload", p)
			}
			return true
		})
	}

	nextPage := gjson.GetBytes(body, "paging.next").String()
	if nextPage != "" {
		nextPages = append(nextPages, nextPage)
	}

	spInfos = mergeSpendings(spInfos)

	return &SpendingFetchingResponse{
		spInfos:   spInfos,
		nextPages: nextPages,
	}, nil
}

func mergeSpendings(infos []domain.Spending) []domain.Spending {
	m := make(map[string]domain.Spending)
	for _, info := range infos {
		key := fmt.Sprintf("%v_%v_%v", info.AdID, info.DateStart, info.HourlyStat)
		v, ok := m[key]
		if !ok {
			m[key] = info
			continue
		}

		/**
		  	"sum(clicks) AS click",
			"avg(cpm) AS cpm",
			"avg(ctr) AS ctr",
			"avg(cpc) AS cpc",
			"avg(impressions) AS impression",
			"sum(spent) AS spent",
		*/
		info.Clicks += v.Clicks
		info.RawNumeric += v.RawNumeric

		info.Impressions = (info.Impressions + v.Impressions) / 2
		info.CPM = (info.CPM + v.CPM) / 2
		info.CPC = (info.CPC + v.CPC) / 2
		info.CTR = (info.CTR + v.CTR) / 2

		m[key] = info
	}

	resp := make([]domain.Spending, 0, len(m))
	for _, v := range m {
		resp = append(resp, v)
	}

	return resp
}

const layout string = "2006-01-02T15:04:05-0700"

func parseTime(raw string) (time.Time, error) {
	parsedTime, err := time.Parse(layout, raw)
	return parsedTime, err
}

func GenerateURL(task string, token string, parentID *int64, params map[string]interface{}) (string, error) {
	switch task {
	case string(TypeFetchAdAccounts):
		return fmt.Sprintf(fetchAdAccountsEndpoint, token), nil
	case string(TypeFetchCampaigns):
		if parentID == nil {
			return "", errors.New("parent ID is nil")
		}
		return fmt.Sprintf(fetchCampaignsEndpoint, *parentID, token), nil
	case string(TypeFetchAdSets):
		if parentID == nil {
			return "", errors.New("parent ID is nil")
		}
		return fmt.Sprintf(fetchAdSetsEndpoint, *parentID, token), nil
	case string(TypeFetchAds):
		if parentID == nil {
			return "", errors.New("parent ID is nil")
		}
		return fmt.Sprintf(fetchAdsEndpoint, *parentID, token), nil
	case string(TypeFetchSpendings):
		if parentID == nil {
			return "", errors.New("parent ID is nil")
		}
		// temporarily
		now := time.Now()
		if params == nil {
			params = make(map[string]interface{})
		}

		since := cast.ToString(params["since"])
		until := cast.ToString(params["until"])
		if len(since) == 0 {
			since = now.Add(-3 * 24 * time.Hour).Format("2006-01-02")
		}
		if len(until) == 0 {
			until = now.Add(14 * time.Hour).Format("2006-01-02")
		}

		timeRange := fmt.Sprintf(`{"since":"%s","until":"%s"}`,
			since,
			until,
		)

		url := fmt.Sprintf(fetchSpendingsEndpoint, *parentID, token) + "&time_range=" + neturl.QueryEscape(timeRange)
		return url, nil
	default:
		return "", errors.New("invalid task type")
	}
}

func extractErrorCode(body []byte) int {
	code := gjson.GetBytes(body, "error.code").Int()
	return int(code)
}

const (
	InvalidTokenCode   = 190
	TooManyRequestCode = 80004
)

var (
	ErrAPIRateLimit         = errors.New("API rate limit exceeded")
	ErrInvalidToken         = errors.New("invalid token: maybe due to expired, change password, etc")
	ErrCreateAsyncJobFailed = errors.New("fail to create async job")
)
