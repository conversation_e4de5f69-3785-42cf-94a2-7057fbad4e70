package adcrawl

import (
	"strings"
	"sync"
	"time"

	"github.com/hibiken/asynq"
	"golang.org/x/exp/rand"
)

const (
	InsertQueueName         = "insert"
	FetchQueueName          = "fetch"
	CriticalInsertQueueName = "critical_insert"
	CriticalFetchQueueName  = "critical_fetch"
)

type Config struct {
	NumberOfWorkers int
	RetryBaseWait   map[string]time.Duration
	Priority        map[string]int
}

type CronCrawler struct {
	srv           *asynq.Server
	insertHandler *InsertTaskHandler
	fetchHandler  *FetchTaskHandler
	seedHandler   *SeedTaskHandler
	updateHandler *UpdateTaskHandler
}

func NewCronCrawler(redisAddr string, ih *InsertTaskHandler, fh *FetchTaskHandler, sh *SeedTaskHandler, uh *UpdateTaskHandler, cfg Config) *CronCrawler {
	c := &CronCrawler{
		srv: asynq.NewServer(asynq.RedisClientOpt{Addr: redisAddr}, asynq.Config{
			Concurrency: cfg.NumberOfWorkers,
			RetryDelayFunc: func(n int, e error, t *asynq.Task) time.Duration {
				taskType := t.Type()
				// since InsertTask has nothing to do with API ratelimit
				// set it retry time for every base wait time set
				if strings.HasPrefix(taskType, "insert") {
					return cfg.RetryBaseWait["insert"]
				} else if strings.HasPrefix(taskType, "fetch") {
					// FetchTask has to deal with API ratelimit
					// exponential backoff with jitter
					upperWait := 2 * time.Duration(n+1) * cfg.RetryBaseWait["fetch"]
					lowerWait := upperWait / 2
					return lowerWait + time.Duration(rand.Int63n(int64(upperWait-lowerWait)))
				} else {
					return cfg.RetryBaseWait["seed"]
				}
			},
			Queues: map[string]int{
				"insert":          cfg.Priority["insert"],
				"fetch":           cfg.Priority["fetch"],
				"critical_fetch":  cfg.Priority["critical_fetch"],
				"critical_insert": cfg.Priority["critical_insert"],
				"seed":            cfg.Priority["seed"],
				"update":          cfg.Priority["update"],
			},
		}),
		insertHandler: ih,
		fetchHandler:  fh,
		seedHandler:   sh,
		updateHandler: uh,
	}
	return c
}

func (c *CronCrawler) Start() {
	mux := asynq.NewServeMux()

	{
		mux.HandleFunc(string(TypeFetchAdAccountIDs), c.fetchHandler.HandleFetchAdAccountIdsTask)
		mux.HandleFunc(string(TypeFetchAdAccounts), c.fetchHandler.HandleFetchAdAccountsTask)
		mux.HandleFunc(string(TypeFetchCampaigns), c.fetchHandler.HandleFetchCampaignsTask)
		mux.HandleFunc(string(TypeFetchAdSets), c.fetchHandler.HandleFetchAdSetsTask)
		mux.HandleFunc(string(TypeFetchAds), c.fetchHandler.HandleFetchAdsTask)
		mux.HandleFunc(string(TypeFetchSpendings), c.fetchHandler.HandleFetchSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeInsertAdAccounts), c.insertHandler.HandleInsertAdAccountsTask)
		mux.HandleFunc(string(TypeInsertCampaigns), c.insertHandler.HandleInsertCampaignsTask)
		mux.HandleFunc(string(TypeInsertAdSets), c.insertHandler.HandleInsertAdSetsTask)
		mux.HandleFunc(string(TypeInsertAds), c.insertHandler.HandleInsertAdsTask)
		mux.HandleFunc(string(TypeInsertSpendings), c.insertHandler.HandleInsertSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeSeedFetchSpendings), c.seedHandler.HandleSeedFetchSpendingsTask)
	}

	{
		mux.HandleFunc(string(TypeUpdateMarketerID), c.updateHandler.HandleUpdateMarketerIDTask)
		mux.HandleFunc(string(TypeUpdateCountryID), c.updateHandler.HandleUpdateCountryIDTask)
	}

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := c.srv.Run(mux); err != nil {
			panic(err)
		}
	}()
	wg.Wait()
}
