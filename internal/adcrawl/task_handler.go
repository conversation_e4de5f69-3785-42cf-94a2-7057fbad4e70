package adcrawl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.com/a7923/athena-go/pkg/config"
	neturl "net/url"
	"time"

	"gitlab.com/a7923/athena-go/pkg/utils"

	"github.com/hibiken/asynq"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

type TaskConfig struct {
	MaxRetry    int
	TaskTimeout time.Duration
}

type FetchTaskHandler struct {
	fetcher    *Fetcher
	taskClient *asynq.Client
	taskCfg    TaskConfig
	tokenRepo  domain.TokenRepository
}

func NewFetchTaskHandler(fetcher *Fetcher, taskClient *asynq.Client, taskCfg TaskConfig, tokenRepo domain.TokenRepository) *FetchTaskHandler {
	return &FetchTaskHandler{
		fetcher:    fetcher,
		taskClient: taskClient,
		taskCfg:    taskCfg,
		tokenRepo:  tokenRepo,
	}
}

func (h *FetchTaskHandler) HandleFetchAdAccountIdsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	response, err := h.fetcher.FetchAdAccountIDs(payload.URL)
	if err != nil {
		return err
	}

	if response.nextPage != "" {
		if err := enqueueNextTask(h.taskClient, task.Type(), response.nextPage, payload.AccessToken, nil,
			asynq.Queue(FetchQueueName), asynq.MaxRetry(h.taskCfg.MaxRetry),
			asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue next task: %s", err.Error())
		}
	}

	for _, id := range response.adAccountIDs {
		fspURL := fmt.Sprintf(fetchSpendingsEndpoint, id)
		fspTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchSpendings), fspURL, payload.AccessToken, nil)
		if err != nil {
			logger.AthenaLogger.Warnf("failed to create fetch spending task: %s", err.Error())
			continue
		}

		if _, err := h.taskClient.Enqueue(fspTask, asynq.Queue(FetchQueueName),
			asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue fetch spending task: %s", err.Error())
		}
	}

	return nil
}

func (h *FetchTaskHandler) HandleFetchAdAccountsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}
	response, err := h.fetcher.FetchAdAccounts(payload.URL)
	if err != nil {
		if errors.Is(err, ErrInvalidToken) {
			// Ref: https://github.com/hibiken/asynq/wiki/Task-Retry#skip-retry
			// This will mark task as no need for retry
			if err := h.tokenRepo.MarkInvalidToken(ctx, payload.AccessToken); err != nil {
				logger.AthenaLogger.Error("failed to mark invalid token", "error", err.Error())
				return err
			}
			return fmt.Errorf("token is invalid, skip retry: %w", asynq.SkipRetry)
		}
		return err
	}
	_, critical := payload.ExtraParams["critical"]
	if response.nextPage != "" {
		nextQueue := FetchQueueName
		if critical {
			nextQueue = CriticalFetchQueueName
		}
		if err := enqueueNextTask(h.taskClient, task.Type(), response.nextPage, payload.AccessToken, payload.ExtraParams,
			asynq.Queue(nextQueue), asynq.MaxRetry(h.taskCfg.MaxRetry),
			asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue next task: %s", err.Error())
		}
	}

	if len(response.adAccountInfos) == 0 {
		return nil
	}

	analyticAccountIDStr, ok := payload.ExtraParams["analytic_account_id"].(string)
	if !ok {
		return errors.New("analytic_account_id is not found in extra params")
	}
	analyticAccountID := cast.ToInt64(analyticAccountIDStr)

	marketerIDStr, ok := payload.ExtraParams["marketer_id"].(string)
	if !ok {
		return errors.New("marketer_id is not found in extra params")
	}
	marketerID := cast.ToInt64(marketerIDStr)

	companyIDStr, ok := payload.ExtraParams["company_id"].(string)
	if !ok {
		return errors.New("company_id is not found in extra params")
	}
	companyID := cast.ToInt64(companyIDStr)

	for i := range response.adAccountInfos {
		response.adAccountInfos[i].AnalyticAccountID = analyticAccountID
		response.adAccountInfos[i].MarketerID = marketerID
		response.adAccountInfos[i].CompanyID = companyID
	}

	_, scheduled := payload.ExtraParams["schedule"]

	insertTask, err := NewInsertAdAccountsTask(response.adAccountInfos, payload.AccessToken, scheduled, critical)
	if err != nil {
		return err
	}

	nextQueue := InsertQueueName
	if critical {
		nextQueue = CriticalInsertQueueName
	}
	if _, err := h.taskClient.Enqueue(insertTask, asynq.Queue(nextQueue),
		asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
		return err
	}

	return nil
}

func (h *FetchTaskHandler) HandleFetchCampaignsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	response, err := h.fetcher.FetchCampaigns(payload.URL)
	if err != nil {
		if errors.Is(err, ErrInvalidToken) {
			// This will mark task as no need for retry
			if err := h.tokenRepo.MarkInvalidToken(ctx, payload.AccessToken); err != nil {
				logger.AthenaLogger.Error("failed to mark invalid token", "error", err.Error())
			}
			return fmt.Errorf("token is invalid, skip retry: %w", asynq.SkipRetry)
		}
		return err
	}
	_, critical := payload.ExtraParams["critical"]

	marketerIDStr, ok := payload.ExtraParams["marketer_id"].(string)
	if !ok {
		return errors.New("marketer_id is not found in extra params")
	}
	marketerID := cast.ToInt64(marketerIDStr)

	companyIDStr, ok := payload.ExtraParams["company_id"].(string)
	if !ok {
		return errors.New("company_id is not found in extra params")
	}
	companyID := cast.ToInt64(companyIDStr)

	if response == nil {
		return nil
	}

	for i := range response.campaignInfos {
		response.campaignInfos[i].MarketerID = marketerID
		response.campaignInfos[i].CompanyID = &companyID
	}

	var nextQueue string
	if critical {
		nextQueue = CriticalFetchQueueName
	} else {
		nextQueue = FetchQueueName
	}
	for _, nextPage := range response.nextPages {
		if err := enqueueNextTask(h.taskClient, task.Type(), nextPage, payload.AccessToken, payload.ExtraParams,
			asynq.Queue(nextQueue), asynq.MaxRetry(h.taskCfg.MaxRetry),
			asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue next task: %s", err.Error())
		}
	}

	if len(response.campaignInfos) == 0 {
		return nil
	}

	_, scheduled := payload.ExtraParams["schedule"]

	insertTask, err := NewInsertCampaignsTask(response.campaignInfos, payload.AccessToken, scheduled, critical)
	if err != nil {
		return err
	}

	nextQueue = InsertQueueName
	if critical {
		nextQueue = CriticalInsertQueueName
	}
	if _, err := h.taskClient.Enqueue(insertTask, asynq.Queue(nextQueue),
		asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
		return err
	}

	return nil
}

func (h *FetchTaskHandler) HandleFetchAdSetsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	response, err := h.fetcher.FetchAdSets(payload.URL)
	if err != nil {
		if errors.Is(err, ErrInvalidToken) {
			// This will mark task as no need for retry
			if err := h.tokenRepo.MarkInvalidToken(ctx, payload.AccessToken); err != nil {
				logger.AthenaLogger.Error("failed to mark invalid token", "error", err.Error())
			}
			return fmt.Errorf("token is invalid, skip retry: %w", asynq.SkipRetry)
		}
		return err
	}

	if response == nil {
		logger.AthenaLogger.Debugw("response is nil", "url", utils.DetectAndCensorAPIKeysTokens(payload.URL))
		return nil
	}

	_, critical := payload.ExtraParams["critical"]

	var nextQueue string
	if critical {
		nextQueue = CriticalFetchQueueName
	} else {
		nextQueue = FetchQueueName
	}
	for _, nextPage := range response.nextPages {
		if err := enqueueNextTask(h.taskClient, task.Type(), nextPage, payload.AccessToken, payload.ExtraParams,
			asynq.Queue(nextQueue), asynq.MaxRetry(h.taskCfg.MaxRetry),
			asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue next task: %s", err.Error())
		}
	}

	if len(response.adsetInfos) == 0 {
		return nil
	}

	_, scheduled := payload.ExtraParams["schedule"]

	insertTask, err := NewInsertAdSetsTask(response.adsetInfos, payload.AccessToken, scheduled, critical)
	if err != nil {
		return err
	}

	nextQueue = InsertQueueName
	if critical {
		nextQueue = CriticalInsertQueueName
	}
	if _, err := h.taskClient.Enqueue(insertTask, asynq.Queue(nextQueue),
		asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
		return err
	}

	return nil
}

func (h *FetchTaskHandler) HandleFetchAdsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	response, err := h.fetcher.FetchAds(payload.URL)
	if err != nil {
		if errors.Is(err, ErrInvalidToken) {
			// This will mark task as no need for retry
			if err := h.tokenRepo.MarkInvalidToken(ctx, payload.AccessToken); err != nil {
				logger.AthenaLogger.Error("failed to mark invalid token", "error", err.Error())
			}
			return fmt.Errorf("token is invalid, skip retry: %w", asynq.SkipRetry)
		}
		return err
	}

	if response == nil {
		return nil
	}

	_, critical := payload.ExtraParams["critical"]

	var nextQueue string
	if critical {
		nextQueue = CriticalFetchQueueName
	} else {
		nextQueue = FetchQueueName
	}
	for _, nextPage := range response.nextPages {
		if err := enqueueNextTask(h.taskClient, task.Type(), nextPage, payload.AccessToken, nil,
			asynq.Queue(nextQueue), asynq.MaxRetry(h.taskCfg.MaxRetry),
			asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Errorf("failed to enqueue next task: %s", err.Error())
		}
	}

	if len(response.adInfos) == 0 {
		return nil
	}

	_, scheduled := payload.ExtraParams["schedule"]

	insertTask, err := NewInsertAdsTask(response.adInfos, payload.AccessToken, scheduled, critical)
	if err != nil {
		logger.AthenaLogger.Warnf("failed to create insert ads task: %s", err.Error())
	} else {
		nextQueue := InsertQueueName
		if critical {
			nextQueue = CriticalInsertQueueName
		}
		if _, err := h.taskClient.Enqueue(insertTask, asynq.Queue(nextQueue),
			asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue insert ads task: %s", err.Error())
		}
	}
	return nil
}

func (h *FetchTaskHandler) HandleFetchSpendingsTask(ctx context.Context, task *asynq.Task) error {
	payload := FetchAdRelatedObjectsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	response, err := h.fetcher.FetchSpendings(payload, 0)
	if err != nil {
		if errors.Is(err, ErrInvalidToken) {
			// This will mark task as no need for retry
			if err := h.tokenRepo.MarkInvalidToken(ctx, payload.AccessToken); err != nil {
				logger.AthenaLogger.Error("failed to mark invalid token", "error", err.Error())
			}
			return fmt.Errorf("token is invalid, skip retry: %w", asynq.SkipRetry)
		}
		return err
	}
	if response == nil {
		return nil
	}
	if payload.ExtraParams == nil {
		payload.ExtraParams = make(map[string]interface{})
	}
	_, critical := payload.ExtraParams["critical"]
	if response.nextPages != nil {
		for _, nextPage := range response.nextPages {
			nextQueue := FetchQueueName
			if critical {
				nextQueue = CriticalFetchQueueName
			}
			if err := enqueueNextTask(h.taskClient, task.Type(), nextPage, payload.AccessToken, payload.ExtraParams,
				asynq.Queue(nextQueue), asynq.MaxRetry(h.taskCfg.MaxRetry),
				asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
				logger.AthenaLogger.Warnf("failed to enqueue next task: %s", err.Error())
			}
		}
	}
	if len(response.spInfos) == 0 {
		return nil
	}
	_, scheduled := payload.ExtraParams["schedule"]

	analyticAccountIDStr, ok := payload.ExtraParams["analytic_account_id"].(string)
	if !ok {
		logger.AthenaLogger.Warnf("failed to get analytic account id from extra params")
	}
	analyticAccountID := cast.ToInt64(analyticAccountIDStr)

	for i := range response.spInfos {
		response.spInfos[i].AnalyticAccountID = analyticAccountID
	}

	insertTask, err := NewInsertSpendingsTask(response.spInfos, scheduled)
	if err != nil {
		logger.AthenaLogger.Warnf("failed to create insert spending task: %s", err.Error())
		return err
	} else {
		nextQueue := InsertQueueName
		if critical {
			nextQueue = CriticalInsertQueueName
		}
		if _, err := h.taskClient.Enqueue(insertTask, asynq.Queue(nextQueue),
			asynq.MaxRetry(h.taskCfg.MaxRetry), asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
			logger.AthenaLogger.Warnf("failed to enqueue insert spending task: %s", err.Error())
			return err
		}
	}

	return nil
}

func enqueueNextTask(taskClient *asynq.Client, tasktype string, url string, token string, extra map[string]interface{}, taskOpts ...asynq.Option) error {
	nextTask, err := NewFetchAdRelatedObjectsTask(tasktype, url, token, extra)
	if err != nil {
		return err
	}

	if _, err := taskClient.Enqueue(nextTask, taskOpts...); err != nil {
		return err
	}

	return nil
}

type InsertTaskHandler struct {
	taskClient    *asynq.Client
	adAccountRepo domain.AdAccountRepository
	campaignRepo  domain.CampaignRepository
	adSetRepo     domain.AdSetRepository
	adRepo        domain.AdRepository
	spendingRepo  domain.SpendingRepository
	marketerRepo  domain.MarketerRepository
	taskCfg       TaskConfig
}

func NewInsertTaskHandler(
	taskClient *asynq.Client,
	adAccountRepo domain.AdAccountRepository,
	campaignRepo domain.CampaignRepository,
	adSetRepo domain.AdSetRepository,
	adRepo domain.AdRepository,
	spendingRepo domain.SpendingRepository,
	marketerRepo domain.MarketerRepository,
	taskCfg TaskConfig,
) *InsertTaskHandler {
	return &InsertTaskHandler{
		taskClient:    taskClient,
		adAccountRepo: adAccountRepo,
		campaignRepo:  campaignRepo,
		adSetRepo:     adSetRepo,
		adRepo:        adRepo,
		spendingRepo:  spendingRepo,
		marketerRepo:  marketerRepo,
		taskCfg:       taskCfg,
	}
}

func (h *InsertTaskHandler) HandleInsertAdAccountsTask(ctx context.Context, task *asynq.Task) error {
	payload := InsertAdAccountsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	if err := h.adAccountRepo.BatchInsert(ctx, payload.AdAccounts, payload.Scheduled); err != nil {
		return err
	}

	extraParams := map[string]interface{}{
		"marketer_id":         cast.ToString(payload.AdAccounts[0].MarketerID),
		"company_id":          cast.ToString(payload.AdAccounts[0].CompanyID),
		"analytic_account_id": cast.ToString(payload.AdAccounts[0].AnalyticAccountID),
	}
	if payload.Scheduled {
		extraParams["schedule"] = true
	}
	if payload.Critical {
		extraParams["critical"] = true
	}

	now := time.Now()
	since := now.Add(-7 * 24 * time.Hour).Format("2006-01-02")
	until := now.Add(14 * time.Hour).Format("2006-01-02")
	timeRange := fmt.Sprintf(`{"since":"%s","until":"%s"}`,
		since,
		until,
	)

	var nextQueue string
	if payload.Critical {
		nextQueue = CriticalFetchQueueName
	} else {
		nextQueue = FetchQueueName
	}

	adAccountIDs := make([]int64, 0, len(payload.AdAccounts))
	for i := range adAccountIDs {
		adAccountIDs[i] = payload.AdAccounts[i].ID
	}
	listMarketerIDResp, err := h.marketerRepo.ListByAdAccountIDs(ctx, payload.AdAccounts[0].CompanyID, adAccountIDs)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to get marketer by ad account ids: %s", err.Error())
		return err
	}
	marketerIDMapper := make(map[int64]int64)
	for _, pair := range listMarketerIDResp.Pairs {
		marketerIDMapper[pair.AdAccountID] = pair.MarketerID
	}

	for i := range payload.AdAccounts {
		go func() {
			fetchCampaignURL := fmt.Sprintf(fetchCampaignsEndpointV2, payload.AdAccounts[i].ID, payload.AccessToken)
			fcTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchCampaigns), fetchCampaignURL, payload.AccessToken, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create fetch campaign task: %s", err.Error())
			} else {
				if _, err := h.taskClient.Enqueue(fcTask, asynq.Queue(nextQueue),
					asynq.MaxRetry(h.taskCfg.MaxRetry),
					asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
					logger.AthenaLogger.Warnf("failed to enqueue fetch campaign task: %s", err.Error())
				}
			}

			fetchAdsetsURL := fmt.Sprintf(fetchAdSetsEndpointV2, payload.AdAccounts[i].ID, payload.AccessToken)
			fasTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchAdSets), fetchAdsetsURL, payload.AccessToken, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create fetch adset task: %s", err.Error())
			} else {
				if _, err := h.taskClient.Enqueue(fasTask, asynq.Queue(nextQueue),
					asynq.MaxRetry(h.taskCfg.MaxRetry),
					asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
					logger.AthenaLogger.Warnf("failed to enqueue fetch adset task: %s", err.Error())
				}
			}

			fetchAdsURL := fmt.Sprintf(fetchAdsEndpointV2, payload.AdAccounts[i].ID, payload.AccessToken)
			faTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchAds), fetchAdsURL, payload.AccessToken, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create fetch ad task: %s", err.Error())
			} else {
				if _, err := h.taskClient.Enqueue(faTask, asynq.Queue(nextQueue),
					asynq.MaxRetry(h.taskCfg.MaxRetry),
					asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
					logger.AthenaLogger.Warnf("failed to enqueue fetch ad task: %s", err.Error())
				}
			}

			// The first time crawl, fetch spendings from the last 7 days
			fetchSpendingURL := fmt.Sprintf(fetchSpendingsFromAdAccountEndpoint, payload.AdAccounts[i].ID, payload.AccessToken)
			fetchSpendingURL += "&time_range=" + neturl.QueryEscape(timeRange)
			fsTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchSpendings), fetchSpendingURL, payload.AccessToken, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create fetch spending task: %s", err.Error())
			} else {
				if _, err := h.taskClient.Enqueue(fsTask, asynq.Queue(nextQueue),
					asynq.MaxRetry(h.taskCfg.MaxRetry),
					asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
					logger.AthenaLogger.Warnf("failed to enqueue fetch spending task: %s", err.Error())
				}
			}
		}()
	}

	return nil
}

func (h *InsertTaskHandler) HandleInsertCampaignsTask(ctx context.Context, task *asynq.Task) error {
	payload := InsertCampaignsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	adAccountIDs := make([]int64, 0)
	for i := range payload.Campaigns {
		adAccountIDs = append(adAccountIDs, payload.Campaigns[i].AdAccountID)
	}
	listMarketerIDResp, err := h.marketerRepo.ListByAdAccountIDs(ctx, *payload.Campaigns[0].CompanyID, adAccountIDs)
	if err != nil {
		logger.AthenaLogger.Errorf("failed to get marketer by ad account ids: %s", err.Error())
		return err
	}
	marketerIDMapper := make(map[int64]int64)
	for _, pair := range listMarketerIDResp.Pairs {
		marketerIDMapper[pair.AdAccountID] = pair.MarketerID
	}
	for i := range payload.Campaigns {
		payload.Campaigns[i].MarketerID = marketerIDMapper[payload.Campaigns[i].AdAccountID]
	}

	if err := h.campaignRepo.BatchInsert(ctx, payload.Campaigns, payload.Scheduled); err != nil {
		return err
	}

	return nil
}

func (h *InsertTaskHandler) HandleInsertAdSetsTask(ctx context.Context, task *asynq.Task) error {
	payload := InsertAdSetsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	if err := h.adSetRepo.BatchInsert(ctx, payload.AdSets, payload.Scheduled); err != nil {
		return err
	}

	return nil
}

func (h *InsertTaskHandler) HandleInsertAdsTask(ctx context.Context, task *asynq.Task) error {
	payload := InsertAdsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}
	if err := h.adRepo.BatchInsert(ctx, payload.Ads); err != nil {
		logger.AthenaLogger.Errorf("failed to insert ads: %v", err)
		return err
	}

	return nil
}

func (h *InsertTaskHandler) HandleInsertSpendingsTask(ctx context.Context, task *asynq.Task) error {
	payload := InsertSpendingsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	if err := h.spendingRepo.BatchInsert(ctx, payload.Spendings); err != nil {
		return err
	}

	return nil
}

type SeedTaskHandler struct {
	taskClient *asynq.Client
	taskCfg    TaskConfig
	adAccRepo  domain.AdAccountRepository
	tokenRepo  domain.TokenRepository
}

func NewSeedTaskHandler(taskClient *asynq.Client, taskCfg TaskConfig, adAccRepo domain.AdAccountRepository, tokenRepo domain.TokenRepository) *SeedTaskHandler {
	return &SeedTaskHandler{
		taskClient: taskClient,
		taskCfg:    taskCfg,
		adAccRepo:  adAccRepo,
		tokenRepo:  tokenRepo,
	}
}

func (h *SeedTaskHandler) HandleSeedFetchSpendingsTask(ctx context.Context, task *asynq.Task) error {
	payload := SeedFetchSpendingsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	activeTokens, err := h.tokenRepo.ListActiveTokens(ctx)
	if err != nil {
		return err
	}

	now := time.Now()
	xDayAgoForCrawler := config.ViperGetDurationWithDefault("crawl.seed.x_day_ago_for_crawler", 3*24*time.Hour)
	since := now.Add(-xDayAgoForCrawler).Format("2006-01-02")
	until := now.Add(14 * time.Hour).Format("2006-01-02")
	timeRange := fmt.Sprintf(`{"since":"%s","until":"%s"}`,
		since,
		until,
	)

	for _, at := range activeTokens {
		adAccIDs, err := h.adAccRepo.GetAllAdAccountIDs(ctx, at.AnalyticAccountID)
		if err != nil {
			return err
		}

		for _, adAccID := range adAccIDs {
			fetchURL := fmt.Sprintf(fetchSpendingsFromAdAccountEndpoint, adAccID, at.Token)
			fetchURL += "&time_range=" + neturl.QueryEscape(timeRange)
			extraParams := map[string]interface{}{
				"analytic_account_id": cast.ToString(at.AnalyticAccountID),
			}
			fetchTask, err := NewFetchAdRelatedObjectsTask(string(TypeFetchSpendings), fetchURL, at.Token, extraParams)
			if err != nil {
				logger.AthenaLogger.Errorf("failed to create fetch spending task", "error", err.Error())
				continue
			}
			if _, err := h.taskClient.Enqueue(fetchTask, asynq.Queue(FetchQueueName),
				asynq.MaxRetry(h.taskCfg.MaxRetry),
				asynq.Timeout(h.taskCfg.TaskTimeout)); err != nil {
				logger.AthenaLogger.Warnf("failed to enqueue fetch spending task: %s", err.Error())
			}
		}
	}

	return nil
}

type UpdateTaskHandler struct {
	taskClient *asynq.Client
	taskCfg    TaskConfig
	spendRepo  domain.SpendingRepository
	camRepo    domain.CampaignRepository
}

func NewUpdateTaskHandler(taskClient *asynq.Client, taskCfg TaskConfig, spendRepo domain.SpendingRepository, camRepo domain.CampaignRepository) *UpdateTaskHandler {
	return &UpdateTaskHandler{
		taskClient: taskClient,
		taskCfg:    taskCfg,
		spendRepo:  spendRepo,
		camRepo:    camRepo,
	}
}

func (h *UpdateTaskHandler) HandleUpdateMarketerIDTask(ctx context.Context, task *asynq.Task) error {
	payload := UpdateMarketerIDPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	if err := h.spendRepo.UpdateMarketerID(ctx); err != nil {
		return err
	}

	return nil
}

func (h *UpdateTaskHandler) HandleUpdateCountryIDTask(ctx context.Context, task *asynq.Task) error {
	payload := UpdateCountryIDPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	if err := h.camRepo.BulkUpdateCountryID(ctx); err != nil {
		return err
	}

	return nil
}
