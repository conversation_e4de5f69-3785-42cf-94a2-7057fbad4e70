package enums

type LeadType int

const (
	LeadNew LeadType = iota
	LeadAfterSale
)

// CareState is the custom enum type for care states
type CareState int

const (
	CareState_New CareState = iota
	CareState_UnassignAttempted
	CareState_Assigned
	CareState_NoAttempt
	CareState_Attempted
	CareState_Potential
	CareState_AwaitingStock
	CareState_Reconfirm
	CareState_Confirmed
	CareState_Failed
	CareState_Lost
	CareState_Junk
	CareState_Temp
)

// String method to provide string representation of the CareState enum
func (cs CareState) String() string {
	return [...]string{
		"new",                // Chưa tiếp nhận (M<PERSON>i)
		"unassign_attempted", // Ch<PERSON>a tiếp nhận (Đ<PERSON> xử lý)
		"assigned",           // Đ<PERSON> tiếp nhận
		"no_attempt",         // <PERSON><PERSON> xử lý (Chưa xử lý)
		"attempted",          // <PERSON><PERSON> xử lý (Đã xử lý)
		"potential",          // <PERSON><PERSON> xử lý (<PERSON><PERSON><PERSON><PERSON> năng)
		"awaiting_stock",     // <PERSON><PERSON><PERSON> đ<PERSON> (<PERSON><PERSON> hàng)
		"reconfirm",          // <PERSON><PERSON><PERSON> đ<PERSON> (<PERSON><PERSON><PERSON> lại)
		"confirmed",          // <PERSON><PERSON><PERSON> đ<PERSON> (<PERSON><PERSON> chố<PERSON>)
		"failed",             // Thất bại
		"lost",               // Hủy (Hủy)
		"junk",               // Hủy (Rác)
		"temp",               // Chốt đơn (Chốt tạm)
	}[cs]
}

const UpdatedBySystem int64 = -3
