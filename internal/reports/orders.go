package reports

import (
	"fmt"
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/internal/enums"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"time"
)

const (
	LayoutTime = "2006-01-02 15:04:05"
)

type OrderReport struct{}

func (t *OrderReport) GetQuery(req *services.OrderDashboardRequest) (squirrel.SelectBuilder, error) {
	selectColumns := getSelectColumns(req)
	groupBy := req.GroupBy

	qb := squirrel.
		Select(selectColumns...)

	if len(groupBy) > 0 && groupBy[0] == string(ProductId) {
		qb = qb.From("base_orders_dashboard o ARRAY JOIN product_items AS product_item")
	} else {
		qb = qb.From("base_orders_dashboard o")
	}
	qb = qb.
		Where(squirrel.Eq{"company_id": req.CompanyId}).
		Where(squirrel.Eq{"country_id": req.CountryId})

	qb = buildOrderDashboardDimensionWhere(req, qb)

	if len(groupBy) > 0 && groupBy[0] == string(ProductId) {
		qb = qb.GroupBy("product_id")
	} else if len(groupBy) > 0 && groupBy[0] != string(Overview) {
		newGroupBy := make([]string, 0)
		for _, v := range groupBy {
			if utils.Includes([]string{string(NewAt), string(ConfirmedAt), string(HandoverAt)}, v) {
				newGroupBy = append(newGroupBy, "date")
			} else {
				newGroupBy = append(newGroupBy, v)
			}
		}
		qb = qb.GroupBy(newGroupBy...)
	}
	qb = qb.
		OrderBy(req.OrderBy...)
	// get all data to return full option data
	if len(req.Action) == 0 {
		qb = qb.Limit(req.Limit)
	}
	qb = qb.Offset(req.Offset)
	return qb, nil
}

func (t *OrderReport) ProcessRow(row map[string]interface{}, req *services.OrderDashboardRequest) (*models.Row, error) {
	if row == nil {
		return nil, nil
	}
	return &models.Row{Values: utils.ToJSONByte(row)}, nil
}

func buildOrderDashboardDimensionWhere(req *services.OrderDashboardRequest, qb squirrel.SelectBuilder) squirrel.SelectBuilder {
	if len(req.ProjectIds) > 0 {
		qb = qb.Where(squirrel.Expr("project_id IN (?)", req.ProjectIds))
	}

	if len(req.ProductIds) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(product_ids, ?)`, req.ProductIds))
	}
	if len(req.MarketerIds) > 0 {
		qb = qb.Where(squirrel.Expr("marketer_id IN (?)", req.MarketerIds))
	}
	if len(req.SaleReps) > 0 {
		qb = qb.Where(squirrel.Expr("sale_rep_id IN (?)", req.SaleReps))
	}
	if len(req.Sources) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(external_sources,?)`, req.Sources))
	}
	if len(req.CarriersCode) > 0 {
		qb = qb.Where(squirrel.Eq{"carrier_code": req.CarriersCode})
	}
	if len(req.TagIds) > 0 {
		qb = qb.Where(squirrel.Expr(`hasAny(tag_ids, ?)`, req.TagIds))
	}
	if len(req.SaleId) > 0 && len(req.CarePageId) > 0 {
		qb = qb.Where(squirrel.Or{
			squirrel.Expr("sale_id IN (?)", req.SaleId),
			squirrel.Expr("care_page_id IN (?)", req.CarePageId),
		})
	}
	if len(req.SaleId) > 0 && len(req.CarePageId) == 0 {
		qb = qb.Where(squirrel.Expr("sale_id IN (?)", req.SaleId))
	}
	if len(req.CarePageId) > 0 && len(req.SaleId) == 0 {
		qb = qb.Where(squirrel.Expr("care_page_id IN (?)", req.CarePageId))
	}
	if len(req.TeamInCharge) > 0 {
		conditions := make([]squirrel.Sqlizer, 0)
		for _, v := range req.TeamInCharge {
			conditions = append(conditions, squirrel.Eq{"team_in_charge": v})
			if v == TeamInChargeTelesale {
				conditions = append(conditions, squirrel.Expr("sale_id IS NOT NULL"))
			}
			if v == TeamInChargeCarePage {
				conditions = append(conditions, squirrel.Expr("care_page_id IS NOT NULL"))
			}
		}
		qb = qb.Where(squirrel.Or(conditions))
	}

	if len(req.OrderStatus) > 0 {
		qb = qb.Where(squirrel.Expr("order_status IN (?)", req.OrderStatus))
	}

	if len(req.GroupBy) > 0 && req.GroupBy[0] == string(SaleRepId) {
		qb = qb.Where(squirrel.Expr("sale_rep_id IN (?)", req.Dids))
	}

	switch req.DateRangeType {
	case NewTime:
		if len(req.StartTime) > 0 {
			qb = qb.Where(squirrel.GtOrEq{"new_at": req.StartTime})
		}
		if len(req.EndTime) > 0 {
			qb = qb.Where(squirrel.Lt{"new_at": req.EndTime})
		}
	case ConfirmationTime:
		if len(req.StartTime) > 0 {
			qb = qb.Where(squirrel.GtOrEq{"confirmed_at": req.StartTime})
		}
		if len(req.EndTime) > 0 {
			qb = qb.Where(squirrel.Lt{"confirmed_at": req.EndTime})
		}
	case HandoverTime:
		if len(req.StartTime) > 0 {
			qb = qb.Where(squirrel.GtOrEq{"handover_at": req.StartTime})
		}
		if len(req.EndTime) > 0 {
			qb = qb.Where(squirrel.Lt{"handover_at": req.EndTime})
		}
	}

	return qb
}

func getSelectColumns(req *services.OrderDashboardRequest) []string {
	selectColumns := make([]string, 0)
	loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	now := time.Now().In(loc)
	yesterday := now.AddDate(0, 0, -1)
	startVN := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
	endVN := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, loc)
	startUTC := startVN.UTC().Format(LayoutTime)
	endUTC := endVN.UTC().Format(LayoutTime)
	startYesterdayVN := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, loc)
	endYesterdayVN := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, loc)
	startYesterdayUTC := startYesterdayVN.UTC().Format(LayoutTime)
	endYesterdayUTC := endYesterdayVN.UTC().Format(LayoutTime)
	if len(req.Action) > 0 {
		selectColumns = append(selectColumns, []string{
			"groupArray(DISTINCT project_id) AS project_ids_data",
			"groupArray(DISTINCT sale_rep_id) AS sale_rep_ids_data",
			"arrayDistinct(arrayFlatten(groupArray(product_ids))) AS product_ids_data",
			"arrayDistinct(arrayFlatten(groupArray(external_sources))) AS external_sources_data",
			"arrayDistinct(arrayFlatten(groupArray(tag_ids))) AS tag_ids_data",
			"groupArray(DISTINCT carrier_code) AS carrier_codes_data",
			"groupArray(DISTINCT marketer_id) AS marketer_ids_data",
		}...)
		return selectColumns
	}
	countTotalSelectColumns := []string{
		fmt.Sprintf("countIf(order_status NOT IN (%d, %d)) AS total_order_without_draft_and_canceled", enums.Draft, enums.Canceled),
		fmt.Sprintf("sumIf(total_quantity, order_status NOT IN (%d, %d)) AS total_quantity_without_draft_and_canceled", enums.Draft, enums.Canceled),
		fmt.Sprintf("sumIf(cod, order_status NOT IN (%d, %d)) AS revenue_without_draft_and_canceled", enums.Draft, enums.Canceled),
		fmt.Sprintf("sumIf(cod, order_status = %d) AS canceled_revenue", enums.Canceled),
		fmt.Sprintf("sumIf(cod, order_status IN (%d, %d)) AS collected_revenue", enums.Delivered, enums.DeliveredCompleted),
	}
	statusSelectColumns := []string{
		fmt.Sprintf("countIf(order_status = %d) AS new_order_total", enums.New),
		fmt.Sprintf("countIf(order_status = %d) AS awaiting_stock_order_total", enums.AwaitingStock),
		fmt.Sprintf("countIf(order_status = %d) AS reconfirm_order_total", enums.Reconfirm),
		fmt.Sprintf("countIf(order_status = %d) AS confirmed_order_total", enums.Confirmed),
		fmt.Sprintf("countIf(order_status = %d) AS preparing_order_total", enums.Preparing),
		fmt.Sprintf("countIf(order_status = %d) AS handling_over_order_total", enums.HandlingOver),
		fmt.Sprintf("countIf(order_status = %d) AS in_transit_order_total", enums.InTransit),
		fmt.Sprintf("countIf(order_status = %d) AS in_delivery_order_total", enums.InDelivery),
		fmt.Sprintf("countIf(order_status = %d) AS delivered_order_total", enums.Delivered),
		fmt.Sprintf("countIf(order_status = %d) AS delivered_completed_order_total", enums.DeliveredCompleted),
		fmt.Sprintf("countIf(order_status = %d) AS failed_delivery_order_total", enums.FailedDelivery),
		fmt.Sprintf("countIf(order_status = %d) AS awaiting_return_order_total", enums.AwaitingReturn),
		fmt.Sprintf("countIf(order_status = %d) AS in_return_order_total", enums.InReturn),
		fmt.Sprintf("countIf(order_status = %d) AS returned_stocked_order_total", enums.ReturnedStocked),
		fmt.Sprintf("countIf(order_status = %d) AS returned_completed_order_total", enums.ReturnedCompleted),
		fmt.Sprintf("countIf(order_status = %d) AS damaged_order_total", enums.Damaged),
		fmt.Sprintf("countIf(order_status = %d) AS damaged_completed_order_total", enums.DamagedCompleted),
		fmt.Sprintf("countIf(order_status = %d) AS canceled_order_total", enums.Canceled),
		fmt.Sprintf("countIf(order_status = %d) AS lost_order_total", enums.Lost),
		fmt.Sprintf("countIf(order_status = %d) AS lost_completed_order_total", enums.LostCompleted),
	}
	for _, groupBy := range req.GroupBy {
		switch groupBy {
		case string(Overview):
			selectColumns = append(selectColumns, []string{
				fmt.Sprintf("countIf(order_status = %d) AS new_order_total", enums.New),
				fmt.Sprintf("countIf(order_status = %d) AS awaiting_stock_order_total", enums.AwaitingStock),
				fmt.Sprintf("countIf(order_status IN (%d, %d)) AS confirmed_order_total", enums.Confirmed, enums.Reconfirm),
				fmt.Sprintf("countIf(order_status IN (%d, %d)) AS handoved_order_total", enums.Preparing, enums.HandlingOver),
				fmt.Sprintf("countIf(order_status IN (%d, %d, %d)) AS in_delivery_order_total", enums.InTransit, enums.InDelivery, enums.FailedDelivery),
				fmt.Sprintf("countIf(order_status IN (%d, %d)) AS delivered_order_total", enums.Delivered, enums.DeliveredCompleted),
				fmt.Sprintf("countIf(order_status IN (%d, %d, %d, %d)) AS returned_order_total", enums.AwaitingReturn, enums.InReturn, enums.ReturnedStocked, enums.ReturnedCompleted),
				fmt.Sprintf("countIf(order_status IN (%d, %d)) AS damaged_order_total", enums.Damaged, enums.DamagedCompleted),
				fmt.Sprintf("countIf(order_status = %d) AS canceled_order_total", enums.Canceled),
				fmt.Sprintf("countIf(order_status IN (%d, %d)) AS lost_order_total", enums.Lost, enums.LostCompleted),
				fmt.Sprintf("sumIf(cod, new_at >= '%v' AND new_at <= '%v') AS today_sale", startUTC, endUTC),
				fmt.Sprintf("sumIf(cod, new_at >= '%v' AND new_at <= '%v') AS yesterday_sale", startYesterdayUTC, endYesterdayUTC),
				fmt.Sprintf("countIf(new_at >= '%v' AND new_at <= '%v') AS today_new_order", startUTC, endUTC),
				fmt.Sprintf("countIf(new_at >= '%v' AND new_at <= '%v') AS yesterday_new_order", startYesterdayUTC, endYesterdayUTC),
				fmt.Sprintf(
					"countIf(new_at >= '%v' AND new_at <= '%v' AND order_status IN (%d, %d, %d)) AS today_confirmed_order",
					startUTC,
					endUTC,
					enums.Confirmed,
					enums.Reconfirm,
					enums.AwaitingStock,
				),
				fmt.Sprintf(
					"countIf(new_at >= '%v' AND new_at <= '%v' AND order_status IN (%d, %d, %d)) AS yesterday_confirmed_order",
					startYesterdayUTC,
					endYesterdayUTC,
					enums.Confirmed,
					enums.Reconfirm,
					enums.AwaitingStock,
				),
				fmt.Sprintf("sumIf(total_quantity, new_at >= '%v' AND new_at <= '%v') AS today_quantity", startUTC, endUTC),
				fmt.Sprintf("sumIf(total_quantity, new_at >= '%v' AND new_at <= '%v') AS yesterday_quantity", startYesterdayUTC, endYesterdayUTC),
			}...)
			selectColumns = append(selectColumns, countTotalSelectColumns...)
		case string(Status):
			selectColumns = append(selectColumns, []string{
				"order_status",
				"count(*) AS total_order",
				"sum(total_quantity) AS quantity_total",
				"sum(cod) AS revenue",
			}...)
		case string(Project):
			var dateField string
			switch req.DateRangeType {
			case NewTime:
				dateField = "new_at"
			case ConfirmationTime:
				dateField = "confirmed_at"
			case HandoverTime:
				dateField = "handover_at"
			}
			selectColumns = append(selectColumns, []string{
				"project_id",
				fmt.Sprintf(
					"sumIf(cod, order_status NOT IN (%d, %d) AND %v >= '%v' AND %v <= '%v') AS today_sale",
					enums.Draft,
					enums.Canceled,
					dateField,
					startUTC,
					dateField,
					endUTC,
				),
			}...)
			selectColumns = append(selectColumns, countTotalSelectColumns...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, statusSelectColumns...)
			}
		case string(NewAt), string(ConfirmedAt), string(HandoverAt):
			selectColumns = append(selectColumns, fmt.Sprintf("toDate(toTimeZone(%s, 'Asia/Ho_Chi_Minh')) AS date", groupBy))
			if len(req.GroupBy) == 1 {
				selectColumns = append(selectColumns, countTotalSelectColumns...)
			}
			//selectColumns = append(selectColumns, countTotalSelectColumns...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, statusSelectColumns...)
			}
		case string(ProductId):
			selectColumns = append(selectColumns, []string{
				"splitByChar(':', product_item)[1] AS product_id",
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status NOT IN (%d, %d)) AS total_quantity_without_draft_and_canceled",
					enums.Draft,
					enums.Canceled,
				),
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status IN (%d, %d, %d, %d, %d)) AS returned_total_quantity ",
					enums.AwaitingReturn,
					enums.InReturn,
					enums.ReturnedStocked,
					enums.ReturnedCompleted,
					enums.Canceled,
				),
				fmt.Sprintf(
					"sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status IN (%d, %d)) AS delivered_total_quantity ",
					enums.Delivered,
					enums.DeliveredCompleted,
				),
			}...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, []string{
					"sum(toFloat64(splitByChar(':', product_item)[2])) AS sold_quantity",
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS new_quantity_total", enums.New),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS awaiting_stock_quantity_total", enums.AwaitingStock),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS reconfirm_quantity_total", enums.Reconfirm),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS confirmed_quantity_total", enums.Confirmed),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS preparing_quantity_total", enums.Preparing),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS handling_over_quantity_total", enums.HandlingOver),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS in_transit_quantity_total", enums.InTransit),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS in_delivery_quantity_total", enums.InDelivery),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS delivered_quantity_total", enums.Delivered),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS delivered_completed_quantity_total", enums.DeliveredCompleted),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS failed_delivery_quantity_total", enums.FailedDelivery),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS awaiting_return_quantity_total", enums.AwaitingReturn),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS in_return_quantity_total", enums.InReturn),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS returned_stocked_quantity_total", enums.ReturnedStocked),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS returned_completed_quantity_total", enums.ReturnedCompleted),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS damaged_quantity_total", enums.Damaged),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS damaged_completed_quantity_total", enums.DamagedCompleted),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS canceled_quantity_total", enums.Canceled),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS lost_quantity_total", enums.Lost),
					fmt.Sprintf("sumIf(toFloat64(splitByChar(':', product_item)[2]), order_status = %d) AS lost_completed_quantity_total", enums.LostCompleted),
				}...)
			}
		case string(SaleRepId):
			selectColumns = append(selectColumns, "sale_rep_id")
			selectColumns = append(selectColumns, countTotalSelectColumns...)
			if req.IsViewDetail {
				selectColumns = append(selectColumns, statusSelectColumns...)
			}
		case string(Carrier):
			selectColumns = append(selectColumns, []string{
				"carrier_code AS carrier_code",
				"countIf((waybill_number = '' OR waybill_number IS NULL) AND (carrier_code = '' OR carrier_code IS NULL)) AS unassigned_total",
				fmt.Sprintf(
					"countIf((waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL) AND order_status NOT IN (%d, %d)) AS assigned_total",
					enums.Draft,
					enums.Canceled,
				),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS new_order_total", enums.New),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS awaiting_stock_order_total", enums.AwaitingStock),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS reconfirm_order_total", enums.Reconfirm),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS confirmed_order_total", enums.Confirmed),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS preparing_order_total", enums.Preparing),
				fmt.Sprintf("countIf(order_status = %d AND (waybill_number != '' AND waybill_number IS NOT NULL) OR (carrier_code != '' AND carrier_code IS NOT NULL)) AS handling_over_order_total", enums.HandlingOver),
				fmt.Sprintf("countIf(order_status = %d) AS in_transit_order_total", enums.InTransit),
				fmt.Sprintf("countIf(order_status = %d) AS in_delivery_order_total", enums.InDelivery),
				fmt.Sprintf("countIf(order_status = %d) AS delivered_order_total", enums.Delivered),
				fmt.Sprintf("countIf(order_status = %d) AS delivered_completed_order_total", enums.DeliveredCompleted),
				fmt.Sprintf("countIf(order_status = %d) AS failed_delivery_order_total", enums.FailedDelivery),
				fmt.Sprintf("countIf(order_status = %d) AS awaiting_return_order_total", enums.AwaitingReturn),
				fmt.Sprintf("countIf(order_status = %d) AS in_return_order_total", enums.InReturn),
				fmt.Sprintf("countIf(order_status = %d) AS returned_stocked_order_total", enums.ReturnedStocked),
				fmt.Sprintf("countIf(order_status = %d) AS returned_completed_order_total", enums.ReturnedCompleted),
				fmt.Sprintf("countIf(order_status = %d) AS damaged_order_total", enums.Damaged),
				fmt.Sprintf("countIf(order_status = %d) AS damaged_completed_order_total", enums.DamagedCompleted),
				fmt.Sprintf("countIf(order_status = %d) AS canceled_order_total", enums.Canceled),
				fmt.Sprintf("countIf(order_status = %d) AS lost_order_total", enums.Lost),
				fmt.Sprintf("countIf(order_status = %d) AS lost_completed_order_total", enums.LostCompleted),
			}...)
		}
	}
	return selectColumns
}
