package token

import (
	"fmt"
	http2 "gitlab.com/a7923/athena-go/internal/http"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"net/http"
	"time"
)

func GetMarketerIds(r *http.Request, userClient services.UserService, marketerIds []int64) ([]int64, error) {
	if http2.GetIsAdminFromRequest(r) {
		return marketerIds, nil
	}

	start := time.Now()
	userId := http2.GetUserIDFromRequest(r)
	if userId < 1 {
		return nil, fmt.Errorf("invalid user id")
	}

	defer func() {
		took := time.Since(start)
		if took > time.Second {
			logger.AthenaLogger.Warnw("Slow request", "user_id", userId, "url", r.URL.Path, "took", took)
		}
	}()

	userPerm, err := userClient.GetUserPermission(r.Context(), &services.GetUserPermissionRequest{
		UserId: userId,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to get user permission: %v", err)
	}

	//if userPerm.HasFullPermission {
	//	return marketerIds, nil
	//}

	if !userPerm.AllowAccessDescendants {
		return []int64{userId}, nil
	}

	dids := append(http2.GetDescendantIdsFromRequest(r), userId)
	if len(marketerIds) == 0 {
		return dids, nil
	}

	intersection := utils.Intersect(dids, marketerIds)
	if len(intersection) == 0 {
		return []int64{-1}, nil // using -1 for not found
	}

	return intersection, nil
}
