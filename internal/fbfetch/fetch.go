package fbfetch

// This file contains all function used for fetching object's informations from Facebook Graph API

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
)

const (
	DefaultBaseURL string = "https://graph.facebook.com/v22.0"
)

func loadBaseURL() string {
	baseURL = viper.GetString("crawl.base_url")
	if baseURL == "" {
		return DefaultBaseURL
	}
	updateEndpoints()
	return baseURL
}

func updateEndpoints() {
	validateTokenEndpoint = baseURL + "/debug_token?input_token=%s&access_token=%s"
	fetchAnalyticAccountEndpoint = baseURL + "/me?fields=id,name,picture&access_token=%s"
	fetchAdAccountIdsEndpoint = baseURL + "/me/adaccounts?limit=100&access_token=%s"
	fetchAdAccountsEndpoint = baseURL + "/me/adaccounts?fields=account_id,name,account_status,currency,timezone_offset_hours_utc&limit=100&access_token=%s"
	fetchCampaignsEndpoint = baseURL + "/act_%s?fields=campaigns{id,effective_status,status,name,account_id,objective}&limit=100&access_token=%s"
	fetchAdSetsEndpoint = baseURL + "/%s?fields=adsets{id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id}&limit=25&access_token=%s"
	fetchAdsEndpoint = baseURL + "/%s?fields=ads{id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id},adset_id}&limit=5&access_token=%s"
	fetchSpendingsEndpoint = baseURL + "/%s/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=25&access_token=%s"
}

var (
	baseURL                      = DefaultBaseURL
	validateTokenEndpoint        = baseURL + "/debug_token?input_token=%s&access_token=%s"
	fetchAnalyticAccountEndpoint = baseURL + "/me?fields=id,name,picture&access_token=%s"
	fetchAdAccountIdsEndpoint    = baseURL + "/me/adaccounts?limit=100&access_token=%s"
	fetchAdAccountsEndpoint      = baseURL + "/me/adaccounts?fields=account_id,name,account_status,currency,timezone_offset_hours_utc&limit=100&access_token=%s"
	fetchCampaignsEndpoint       = baseURL + "/act_%s?fields=campaigns{id,effective_status,status,name,account_id,objective}&limit=100&access_token=%s"
	fetchAdSetsEndpoint          = baseURL + "/%s?fields=adsets{id,name,status,effective_status,optimization_goal,destination_type,campaign_id,account_id}&limit=25&access_token=%s"
	fetchAdsEndpoint             = baseURL + "/%s?fields=ads{id,name,status,effective_status,created_time,adcreatives{object_story_id,object_story_spec,image_url,place_page_set_id},adset_id}&limit=5&access_token=%s"
	fetchSpendingsEndpoint       = baseURL + "/%s/insights?fields=ad_id,date_start,spend,cpm,cpc,ctr,clicks,frequency,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions,video_avg_time_watched_actions,impressions,account_id,date_stop&date_preset=last_3d&time_increment=1&breakdowns=hourly_stats_aggregated_by_advertiser_time_zone&limit=25&access_token=%s"
)

type Fetcher struct {
	httpClient *http.Client
}

type FetcherConfig struct {
	RequestTimeout      time.Duration
	MaxIdleConnsPerHost int
}

const (
	DefaultRequestTimeout      = 35 * time.Second
	DefaultMaxIdleConnsPerHost = 25
)

func NewFetcher(cfg FetcherConfig) *Fetcher {
	baseURL = loadBaseURL()
	if cfg.MaxIdleConnsPerHost == 0 {
		cfg.MaxIdleConnsPerHost = DefaultMaxIdleConnsPerHost
	}
	if cfg.RequestTimeout == time.Duration(0) {
		cfg.RequestTimeout = DefaultRequestTimeout
	}

	transport := &http.Transport{
		MaxIdleConnsPerHost: cfg.MaxIdleConnsPerHost,
	}
	return &Fetcher{
		httpClient: &http.Client{
			Transport: transport,
			Timeout:   cfg.RequestTimeout,
		},
	}
}

func (f *Fetcher) ValidateToken(token string) (bool, error) {
	url := fmt.Sprintf(validateTokenEndpoint, token, token)
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}

	if resp.StatusCode != 200 {
		errMsg := gjson.GetBytes(body, "error.message").String()
		return false, fmt.Errorf("failed to validate token: %s", errMsg)
	}

	return gjson.GetBytes(body, "data.is_valid").Bool(), nil
}

func (f *Fetcher) FetchAnalyticAccount(token string) (*domain.AnalyticAccount, error) {
	url := fmt.Sprintf(fetchAnalyticAccountEndpoint, token)
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("failed to fetch analytic account: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	acc := domain.AnalyticAccount{
		Username:      gjson.GetBytes(body, "name").String(),
		ID:            cast.ToInt64(gjson.GetBytes(body, "id").String()),
		ProfilePicURL: gjson.GetBytes(body, "picture.data.url").String(),
	}

	return &acc, nil
}

type AdAccountIDFetchingResponse struct {
	adAccountIDs []string
	nextPage     string
}

func (f *Fetcher) FetchAdAccountIDs(p FetchObjectPayload) (*AdAccountIDFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchAdAccountIdsEndpoint, p.AccessToken)
	}

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var adAccountIds []string
	results := gjson.GetBytes(body, "data.#.account_id")
	results.ForEach(func(_, value gjson.Result) bool {
		adAccountIds = append(adAccountIds, value.String())
		return true
	})

	nextPage := gjson.GetBytes(body, "paging.next").String()

	return &AdAccountIDFetchingResponse{
		adAccountIDs: adAccountIds,
		nextPage:     nextPage,
	}, nil
}

type AdAccountFetchingResponse struct {
	AdAccounts []domain.AdAccount
	NextPage   string
}

func (f *Fetcher) FetchAdAccounts(p FetchObjectPayload) (*AdAccountFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchAdAccountsEndpoint, p.AccessToken)
	}
	log.Println(url)
	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var adAccs []domain.AdAccount
	adAccJSON := gjson.GetBytes(body, "data").String()
	if resp.StatusCode == 200 && len(adAccJSON) == 0 {
		return &AdAccountFetchingResponse{
			AdAccounts: adAccs,
		}, nil
	}

	var dummyAdAccInfos []domain.DummyAdAccount
	if err := json.Unmarshal([]byte(adAccJSON), &dummyAdAccInfos); err != nil {
		return nil, err
	}

	for i := range dummyAdAccInfos {
		adAccs = append(adAccs, dummyAdAccInfos[i].ToAdAccount())
	}

	nextPage := gjson.GetBytes(body, "paging.next").String()

	return &AdAccountFetchingResponse{
		AdAccounts: adAccs,
		NextPage:   nextPage,
	}, nil
}

type CampaignFetchingResponse struct {
	Campaigns []domain.Campaign
	NextPage  string
}

func (f *Fetcher) FetchCampaigns(p FetchObjectPayload) (*CampaignFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchCampaignsEndpoint, p.ExtraParams["AdAccountID"], p.AccessToken)
	}
	log.Println(url)

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var campaigns []domain.Campaign
	campaignJSON := gjson.GetBytes(body, "campaigns.data").String()
	if resp.StatusCode == 200 && len(campaignJSON) == 0 {
		return &CampaignFetchingResponse{
			Campaigns: campaigns,
		}, nil
	}

	var dummyCampaignInfos []domain.DummyCampaign
	if err := json.Unmarshal([]byte(campaignJSON), &dummyCampaignInfos); err != nil {
		return nil, err
	}

	for i := range dummyCampaignInfos {
		campaigns = append(campaigns, dummyCampaignInfos[i].ToCampaign())
	}

	nextPage := gjson.GetBytes(body, "campaigns.paging.next").String()

	return &CampaignFetchingResponse{
		Campaigns: campaigns,
		NextPage:  nextPage,
	}, nil
}

type AdSetFetchingResponse struct {
	Adsets   []domain.AdSet
	NextPage string
}

func (f *Fetcher) FetchAdSets(p FetchObjectPayload) (*AdSetFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchAdSetsEndpoint, p.ExtraParams["CampaignID"], p.AccessToken)
	}

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var adsets []domain.AdSet
	adsetJSON := gjson.GetBytes(body, "adsets.data").String()
	if resp.StatusCode == 200 && len(adsetJSON) == 0 {
		return &AdSetFetchingResponse{
			Adsets: adsets,
		}, nil
	}

	var dummyAdsetInfos []domain.DummyAdSet
	if err := json.Unmarshal([]byte(adsetJSON), &dummyAdsetInfos); err != nil {
		return nil, err
	}

	for i := range dummyAdsetInfos {
		adsets = append(adsets, dummyAdsetInfos[i].ToAdSet())
	}

	nextPage := gjson.GetBytes(body, "adsets.paging.next").String()

	return &AdSetFetchingResponse{
		Adsets:   adsets,
		NextPage: nextPage,
	}, nil
}

type AdFetchingResponse struct {
	Ads      []domain.Ad
	NextPage string
}

func (f *Fetcher) FetchAds(p FetchObjectPayload) (*AdFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchAdsEndpoint, p.ExtraParams["AdSetID"], p.AccessToken)
	}

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var adInfos []domain.Ad
	adJSON := gjson.GetBytes(body, "ads.data").String()
	if resp.StatusCode == 200 && len(adJSON) == 0 {
		return &AdFetchingResponse{
			Ads: adInfos,
		}, nil
	}

	var dummyAdInfos []domain.DummyAd
	if err := json.Unmarshal([]byte(adJSON), &dummyAdInfos); err != nil {
		return nil, err
	}
	for i := range dummyAdInfos {
		adInfos = append(adInfos, dummyAdInfos[i].ToAd())
	}

	created_time := gjson.GetBytes(body, "ads.data.#.created_time")
	currentID := 0
	created_time.ForEach(func(_, value gjson.Result) bool {
		createdTime, err := parseTime(value.String())
		if err != nil {
			slog.Error("failed to parse created time", "error", err.Error(), "payload", value.String())
			return true
		}
		adInfos[currentID].CreatedAt = createdTime
		currentID++
		return true
	})

	currentID = 0
	gjson.GetBytes(body, "ads.data").ForEach(func(_, value gjson.Result) bool {
		pageID := value.Get("adcreatives.data.0.object_story_spec.page_id").String()
		if pageID != "" {
			adInfos[currentID].PageID = pageID
		}

		objectStoryID := value.Get("adcreatives.data.0.object_story_id").String()
		if objectStoryID != "" {
			adInfos[currentID].ObjectStoryID = &objectStoryID
			if pageID == "" {
				adInfos[currentID].PageID = strings.Split(objectStoryID, "_")[0]
			}
		}

		objectStorySpec := value.Get("adcreatives.data.0.object_story_spec").String()
		if objectStorySpec != "" {
			adInfos[currentID].ObjectStorySpec = []byte(objectStorySpec)
		} else {
			adInfos[currentID].ObjectStorySpec = nil
		}

		imageURL := value.Get("adcreatives.data.0.object_story_spec.video_data.image_url").String()
		if imageURL != "" {
			adInfos[currentID].ImageURL = &imageURL
		}

		currentID++
		return true
	})

	if currentID != len(adInfos) {
		slog.Warn("Seem like there're something wrong inside parsing JSON data", "error", "currentID != len(adInfos)")
	}

	nextPage := gjson.GetBytes(body, "ads.paging.next").String()

	return &AdFetchingResponse{
		Ads:      adInfos,
		NextPage: nextPage,
	}, nil
}

type SpendingFetchingResponse struct {
	Spendings []domain.Spending
	NextPages []string
}

func (f *Fetcher) FetchSpendings(p FetchObjectPayload) (*SpendingFetchingResponse, error) {
	var url string
	if p.URL != "" {
		url = p.URL
	} else {
		url = fmt.Sprintf(fetchSpendingsEndpoint, p.ExtraParams["AdID"], p.AccessToken)
	}

	resp, err := f.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var spInfos []domain.Spending
	dataJSON := gjson.GetBytes(body, "data.#.insights")
	if resp.StatusCode == 200 && len(dataJSON.Raw) == 0 {
		return &SpendingFetchingResponse{
			Spendings: spInfos,
		}, nil
	}

	nextPages := make([]string, 0)
	if dataJSON.String() != "[]" {
		dataJSON.ForEach(func(key, value gjson.Result) bool {
			data := value.String()
			if data == "" {
				return true
			}

			spJSON := gjson.Get(data, "data")
			spJSON.ForEach(func(key, value gjson.Result) bool {
				var dummySp domain.DummySpending
				payload := value.String()
				if payload == "" {
					return true
				}
				if err := json.Unmarshal([]byte(payload), &dummySp); err != nil {
					log.Printf("failed to unmarshal spending data: %v", err)

				} else {
					spPtr := domain.ConvertDummySpendingToSpending(dummySp)
					if spPtr != nil {
						spInfos = append(spInfos, *spPtr)
					}
				}
				return true
			})
			nextPage := gjson.Get(data, "paging.next").String()
			if nextPage != "" {
				nextPages = append(nextPages, nextPage)
			}
			return true
		})

		adAccountID := gjson.GetBytes(body, "id").String()
		adAccountID = strings.TrimPrefix(adAccountID, "act_")
		for i := range spInfos {
			spInfos[i].AdAccountID = cast.ToInt64(adAccountID)
		}
	} else {
		spJSON := gjson.GetBytes(body, "data")
		spJSON.ForEach(func(key, value gjson.Result) bool {
			var dummySp domain.DummySpending
			payload := value.String()
			if payload == "" {
				return true
			}
			if err := json.Unmarshal([]byte(payload), &dummySp); err != nil {
				log.Printf("failed to unmarshal spending data: %v", err)

			} else {
				spPtr := domain.ConvertDummySpendingToSpending(dummySp)
				if spPtr != nil {
					spInfos = append(spInfos, *spPtr)
				}
			}
			return true
		})

		adAccountID, err := getAccountID(url)
		if err != nil {
			slog.Error("failed to get account ID", "error", err.Error())
		} else {
			for i := range spInfos {
				spInfos[i].AdAccountID = cast.ToInt64(adAccountID)
			}
		}
	}

	nextPage := gjson.GetBytes(body, "paging.next").String()
	if nextPage != "" {
		nextPages = append(nextPages, nextPage)
	}

	return &SpendingFetchingResponse{
		Spendings: spInfos,
		NextPages: nextPages,
	}, nil
}

const layout string = "2006-01-02T15:04:05-0700"

func parseTime(raw string) (time.Time, error) {
	parsedTime, err := time.Parse(layout, raw)
	return parsedTime, err
}

func getAccountID(url string) (string, error) {
	parts := strings.Split(url, "/")
	if len(parts) < 5 {
		return "", fmt.Errorf("invalid URL format")
	}

	return parts[4], nil // The account ID is the 5th element (index 4)
}
