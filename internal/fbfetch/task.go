package fbfetch

import (
	"errors"
	"fmt"
)

type TaskType string

const (
	TypeFetchAdAccountIDs TaskType = "fetch:adaccount_ids"
	TypeFetchAdAccounts   TaskType = "fetch:adccounts"
	TypeFetchCampaigns    TaskType = "fetch:campaigns"
	TypeFetchAdSets       TaskType = "fetch:adsets"
	TypeFetchAds          TaskType = "fetch:ads"
	TypeFetchSpendings    TaskType = "fetch:spendings"
)

type FetchObjectPayload struct {
	Type        TaskType
	URL         string
	AccessToken string
	ExtraParams map[string]interface{}
}

var ErrUnknownTaskType error = errors.New("unknown task type")

func CreateFetchTask(taskType TaskType, accessToken string, extraParams map[string]interface{}) (*FetchObjectPayload, error) {
	var url string
	switch taskType {
	case TypeFetchAdAccountIDs:
		url = fmt.Sprintf(fetchAdAccountIdsEndpoint, accessToken)
	case TypeFetchAdAccounts:
		url = fmt.Sprintf(fetchAdAccountsEndpoint, accessToken)
	case TypeFetchCampaigns:
		url = fmt.Sprintf(fetchCampaignsEndpoint, extraParams["AdAccountID"], accessToken)
	case TypeFetchAdSets:
		url = fmt.Sprintf(fetchAdSetsEndpoint, extraParams["CampaignID"], accessToken)
	case TypeFetchAds:
		url = fmt.Sprintf(fetchAdsEndpoint, extraParams["AdsetID"], accessToken)
	case TypeFetchSpendings:
		url = fmt.Sprintf(fetchSpendingsEndpoint, extraParams["AdID"], accessToken)
	default:
		return nil, ErrUnknownTaskType
	}

	return &FetchObjectPayload{
		Type:        taskType,
		URL:         url,
		AccessToken: accessToken,
		ExtraParams: extraParams,
	}, nil
}
