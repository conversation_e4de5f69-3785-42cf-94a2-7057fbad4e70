package handlers

import (
	"context"
	"encoding/json"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/internal/fbfetch"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

const (
	QueueName = "crawl-consumer"
)

type CrawlHandler struct {
	Publisher    app.PublisherInterface
	MarketClient services.MarketService
	Fetcher      *fbfetch.Fetcher
	Name         string
}

func NewCrawlHandler(pub app.PublisherInterface, mc services.MarketService, f *fbfetch.Fetcher) *CrawlHandler {
	return &CrawlHandler{
		Publisher:    pub,
		MarketClient: mc,
		Fetcher:      f,
		Name:         Queue<PERSON><PERSON>,
	}
}

func (h *CrawlHandler) GetName() string {
	return h.Name
}

func (h *CrawlHandler) Init() error {
	h.MarketClient = internal.CreateMarketClient(nil)
	return nil
}

func (h *CrawlHandler) HandleMessage(msg *message.Message) error {
	logger.AthenaLogger.Debugw("Received ", "message", string(msg.Payload))

	var payload fbfetch.FetchObjectPayload
	if err := json.Unmarshal(msg.Payload, &payload); err != nil {
		logger.AthenaLogger.Errorw("Error unmarshalling message payload", "error", err)
		return err
	}

	switch payload.Type {
	case fbfetch.TypeFetchAdAccountIDs:
		if err := h.handleFetchAdAccountIDs(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch ad account IDs", "error", err)
			return err
		}
	case fbfetch.TypeFetchAdAccounts:
		if err := h.handleFetchAdAccounts(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch ad accounts", "error", err)
			return err
		}
	case fbfetch.TypeFetchCampaigns:
		if err := h.handleFetchCampaigns(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch campaigns", "error", err)
			return err
		}
	case fbfetch.TypeFetchAdSets:
		if err := h.handleFetchAdsets(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch adsets", "error", err)
			return err
		}
	case fbfetch.TypeFetchAds:
		if err := h.handleFetchAds(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch ads", "error", err)
			return err
		}
	case fbfetch.TypeFetchSpendings:
		if err := h.handleFetchSpendings(payload); err != nil {
			logger.AthenaLogger.Errorw("Error handling fetch spendings", "error", err)
			return err
		}
	default:
		logger.AthenaLogger.Errorw("Unknown task type", "type", payload.Type)
		return fbfetch.ErrUnknownTaskType
	}

	return nil
}

func (h *CrawlHandler) SetPublisher(p app.PublisherInterface) {
	h.Publisher = p
}

func (h *CrawlHandler) Close() {
}

func (h *CrawlHandler) handleFetchAdAccountIDs(p fbfetch.FetchObjectPayload) error {
	return nil
}

// TODO: add timeout for batch insert request
func (h *CrawlHandler) handleFetchAdAccounts(p fbfetch.FetchObjectPayload) error {
	fetchResp, err := h.Fetcher.FetchAdAccounts(p)
	if err != nil {
		logger.AthenaLogger.Errorw("Error fetching ad accounts", "error", err)
		return err
	}

	if fetchResp.NextPage != "" {
		go func() {
			nxtPayload := fbfetch.FetchObjectPayload{
				Type:        fbfetch.TypeFetchAdAccounts,
				URL:         fetchResp.NextPage,
				AccessToken: p.AccessToken,
				ExtraParams: p.ExtraParams,
			}
			data, err := json.Marshal(nxtPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling next page payload", "error", err)
				return
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing next page payload", "error", err)
				return
			}
		}()
	}

	if len(fetchResp.AdAccounts) == 0 {
		return nil
	}

	analyticAccountIDStr, ok := p.ExtraParams["AnalyticAccountID"].(string)
	if !ok {
		logger.AthenaLogger.Errorw("AnalyticAccountID not found in extra params")
		return nil
	}
	analyticAccountID := cast.ToInt64(analyticAccountIDStr)

	marketerIDStr, ok := p.ExtraParams["MarketerID"].(string)
	if !ok {
		logger.AthenaLogger.Errorw("MarketerID not found in extra params")
		return nil
	}
	marketerID := cast.ToInt64(marketerIDStr)

	for i := range fetchResp.AdAccounts {
		fetchResp.AdAccounts[i].AnalyticAccountID = analyticAccountID
		fetchResp.AdAccounts[i].MarketerID = marketerID
		fetchResp.AdAccounts[i].UpdatedByID = marketerID
	}
	batchInsertReq := &services.BatchInsertAdAccountsRequest{
		Adaccounts: convertAdAccountDomainToProto(fetchResp.AdAccounts),
	}
	if _, err := h.MarketClient.BatchInsertAdAccounts(context.Background(), batchInsertReq); err != nil {
		logger.AthenaLogger.Errorw("Error inserting ad accounts", "error", err)
		return err
	}

	// Publish message to trigger crawling campaigns
	go func() {
		fetchPayload := fbfetch.FetchObjectPayload{
			Type:        fbfetch.TypeFetchCampaigns,
			URL:         "",
			AccessToken: p.AccessToken,
		}
		for i := range fetchResp.AdAccounts {
			fetchPayload.ExtraParams = map[string]interface{}{
				"AdAccountID": cast.ToString(fetchResp.AdAccounts[i].ID),
				"MarketerID":  cast.ToString(marketerID),
			}
			data, err := json.Marshal(fetchPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling fetch payload", "error", err)
				continue
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing fetch campaigns payload", "error", err)
				continue
			}
		}
	}()
	return nil
}

func (h *CrawlHandler) handleFetchCampaigns(p fbfetch.FetchObjectPayload) error {
	fetchResp, err := h.Fetcher.FetchCampaigns(p)
	if err != nil {
		logger.AthenaLogger.Errorw("Error fetching campaigns", "error", err)
		return err
	}

	if fetchResp.NextPage != "" {
		go func() {
			nxtPayload := fbfetch.FetchObjectPayload{
				Type:        fbfetch.TypeFetchCampaigns,
				URL:         fetchResp.NextPage,
				AccessToken: p.AccessToken,
				ExtraParams: p.ExtraParams,
			}
			data, err := json.Marshal(nxtPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling next page payload", "error", err)
				return
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing next page payload", "error", err)
				return
			}
		}()
	}
	if len(fetchResp.Campaigns) == 0 {
		return nil
	}

	marketerIDStr, ok := p.ExtraParams["MarketerID"].(string)
	if !ok {
		logger.AthenaLogger.Errorw("MarketerID not found in extra params")
		return nil
	}
	marketerID := cast.ToInt64(marketerIDStr)

	adAccountIDStr, ok := p.ExtraParams["AdAccountID"].(string)
	if !ok {
		logger.AthenaLogger.Errorw("AdAccountID not found in extra params")
		return nil
	}
	adAccountID := cast.ToInt64(adAccountIDStr)

	for i := range fetchResp.Campaigns {
		fetchResp.Campaigns[i].AdAccountID = adAccountID
		fetchResp.Campaigns[i].MarketerID = marketerID
		fetchResp.Campaigns[i].UpdatedByID = marketerID
	}
	batchInsertReq := &services.BatchInsertCampaignsRequest{
		Campaigns: convertCampaignDomainToProto(fetchResp.Campaigns),
	}
	if _, err := h.MarketClient.BatchInsertCampaigns(context.Background(), batchInsertReq); err != nil {
		logger.AthenaLogger.Errorw("Error inserting campaigns", "error", err)
		return err
	}

	// Publish message to trigger crawling adsets
	go func() {
		fetchPayload := fbfetch.FetchObjectPayload{
			Type:        fbfetch.TypeFetchAdSets,
			URL:         "",
			AccessToken: p.AccessToken,
		}
		for i := range fetchResp.Campaigns {
			fetchPayload.ExtraParams = map[string]interface{}{
				"CampaignID": cast.ToString(fetchResp.Campaigns[i].ID),
			}
			data, err := json.Marshal(fetchPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling fetch payload", "error", err)
				continue
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing fetch adsets payload", "error", err)
				continue
			}
		}
	}()

	return nil
}

func (h *CrawlHandler) handleFetchAdsets(p fbfetch.FetchObjectPayload) error {
	fetchResp, err := h.Fetcher.FetchAdSets(p)
	if err != nil {
		logger.AthenaLogger.Errorw("Error fetching adsets", "error", err)
		return err
	}

	if fetchResp.NextPage != "" {
		go func() {
			nxtPayload := fbfetch.FetchObjectPayload{
				Type:        fbfetch.TypeFetchAdSets,
				URL:         fetchResp.NextPage,
				AccessToken: p.AccessToken,
				ExtraParams: p.ExtraParams,
			}
			data, err := json.Marshal(nxtPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling next page payload", "error", err)
				return
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing next page payload", "error", err)
				return
			}
		}()
	}

	if len(fetchResp.Adsets) == 0 {
		return nil
	}

	batchInsertReq := &services.BatchInsertAdsetsRequest{
		Adsets: convertAdsetDomainToProto(fetchResp.Adsets),
	}
	if _, err := h.MarketClient.BatchInsertAdsets(context.Background(), batchInsertReq); err != nil {
		logger.AthenaLogger.Errorw("Error inserting adsets", "error", err)
		return err
	}

	// Publish message to trigger crawling ads
	go func() {
		fetchPayload := fbfetch.FetchObjectPayload{
			Type:        fbfetch.TypeFetchAds,
			URL:         "",
			AccessToken: p.AccessToken,
		}
		for i := range fetchResp.Adsets {
			fetchPayload.ExtraParams = map[string]interface{}{
				"AdSetID": cast.ToString(fetchResp.Adsets[i].ID),
			}
			data, err := json.Marshal(fetchPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling fetch payload", "error", err)
				continue
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing fetch ads payload", "error", err)
				continue
			}
		}
	}()

	return nil
}

func (h *CrawlHandler) handleFetchAds(p fbfetch.FetchObjectPayload) error {
	fetchResp, err := h.Fetcher.FetchAds(p)
	if err != nil {
		logger.AthenaLogger.Errorw("Error fetching ads", "error", err)
		return err
	}

	if fetchResp.NextPage != "" {
		go func() {
			nxtPayload := fbfetch.FetchObjectPayload{
				Type:        fbfetch.TypeFetchAds,
				URL:         fetchResp.NextPage,
				AccessToken: p.AccessToken,
				ExtraParams: p.ExtraParams,
			}
			data, err := json.Marshal(nxtPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling next page payload", "error", err)
				return
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing next page payload", "error", err)
				return
			}
		}()
	}
	if len(fetchResp.Ads) == 0 {
		return nil
	}

	batchInsertReq := &services.BatchInsertAdsRequest{
		Ads: convertAdDomainToProto(fetchResp.Ads),
	}
	if _, err := h.MarketClient.BatchInsertAds(context.Background(), batchInsertReq); err != nil {
		logger.AthenaLogger.Errorw("Error inserting ads", "error", err)
		return err
	}

	// Publish message to trigger crawling spendings
	go func() {
		fetchPayload := fbfetch.FetchObjectPayload{
			Type:        fbfetch.TypeFetchSpendings,
			URL:         "",
			AccessToken: p.AccessToken,
		}
		for i := range fetchResp.Ads {
			fetchPayload.ExtraParams = map[string]interface{}{
				"AdID": cast.ToString(fetchResp.Ads[i].ID),
			}
			data, err := json.Marshal(fetchPayload)
			if err != nil {
				logger.AthenaLogger.Errorw("Error marshalling fetch payload", "error", err)
				continue
			}
			if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
				logger.AthenaLogger.Errorw("Error publishing fetch spendings payload", "error", err)
				continue
			}
		}
	}()

	return nil
}

func (h *CrawlHandler) handleFetchSpendings(p fbfetch.FetchObjectPayload) error {
	fetchResp, err := h.Fetcher.FetchSpendings(p)
	if err != nil {
		logger.AthenaLogger.Errorw("Error fetching spendings", "error", err)
		return err
	}

	if fetchResp.NextPages != nil && len(fetchResp.NextPages) > 0 {
		for _, nxtPage := range fetchResp.NextPages {
			go func(nxtPage string) {
				nxtPayload := fbfetch.FetchObjectPayload{
					Type:        fbfetch.TypeFetchSpendings,
					URL:         nxtPage,
					AccessToken: p.AccessToken,
					ExtraParams: p.ExtraParams,
				}
				data, err := json.Marshal(nxtPayload)
				if err != nil {
					logger.AthenaLogger.Errorw("Error marshalling next page payload", "error", err)
					return
				}
				if err = h.Publisher.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
					logger.AthenaLogger.Errorw("Error publishing next page payload", "error", err)
					return
				}
			}(nxtPage)
		}
	}
	if len(fetchResp.Spendings) == 0 {
		return nil
	}

	batchInsertReq := &services.BatchInsertSpendingsRequest{
		Spendings: convertSpendingsDomainToProto(fetchResp.Spendings),
	}
	if _, err := h.MarketClient.BatchInsertSpendings(context.Background(), batchInsertReq); err != nil {
		logger.AthenaLogger.Errorw("Error inserting spendings", "error", err)
		return err
	}

	return nil
}
