package handlers

import (
	"fmt"
	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
)

func convertAdAccountDomainToProto(dmn []domain.AdAccount) []*models.AdAccount {
	adAccounts := make([]*models.AdAccount, len(dmn))
	for i := range dmn {
		adAccounts[i] = &models.AdAccount{
			Id:                     dmn[i].ID,
			Name:                   dmn[i].Name,
			Status:                 domain.AdAccountStatusIntToStr(dmn[i].Status),
			Currency:               dmn[i].Currency,
			TimezoneOffsetHoursUtc: dmn[i].TimezoneOffsetHoursUTC,
			AnalyticAccountId:      dmn[i].AnalyticAccountID,
			MarketerId:             &dmn[i].MarketerID,
			UpdatedById:            &dmn[i].UpdatedByID,
		}
	}
	return adAccounts
}

func convertCampaignDomainToProto(dmn []domain.Campaign) []*models.Campaign {
	camps := make([]*models.Campaign, len(dmn))
	for i := range dmn {
		camps[i] = &models.Campaign{
			Id:                dmn[i].ID,
			Name:              dmn[i].Name,
			Status:            dmn[i].Status,
			EffectiveStatus:   dmn[i].EffectiveStatus,
			AdaccountId:       dmn[i].AdAccountID,
			Objective:         &dmn[i].Objective,
			MarketerId:        &dmn[i].MarketerID,
			AnalyticAccountId: &dmn[i].AnalyticAccountID,
			UpdatedById:       &dmn[i].UpdatedByID,
		}
	}
	return camps
}

func convertAdsetDomainToProto(dmn []domain.AdSet) []*models.Adset {
	adsets := make([]*models.Adset, len(dmn))
	for i := range dmn {
		adsets[i] = &models.Adset{
			Id:               dmn[i].ID,
			Name:             dmn[i].Name,
			Status:           dmn[i].Status,
			EffectiveStatus:  dmn[i].EffectiveStatus,
			OptimizationGoal: dmn[i].OptimizationGoal,
			AdaccountId:      dmn[i].AdAccountID,
			CampaignId:       dmn[i].CampaignID,
		}
	}
	return adsets
}

func convertAdDomainToProto(dmn []domain.Ad) []*models.Ad {
	ads := make([]*models.Ad, len(dmn))
	for i := range dmn {
		ads[i] = &models.Ad{
			Id:              dmn[i].ID,
			Name:            dmn[i].Name,
			Status:          dmn[i].Status,
			EffectiveStatus: dmn[i].EffectiveStatus,
			PageId:          dmn[i].PageID,
			ObjectStoryId:   dmn[i].ObjectStoryID,
			ObjectStorySpec: dmn[i].ObjectStorySpec,
			AdsetId:         dmn[i].AdSetID,
			ImageUrl:        dmn[i].ImageURL,
		}
	}
	return ads
}

func convertSpendingsDomainToProto(dmn []domain.Spending) []*models.Spending {
	spendings := make([]*models.Spending, len(dmn))
	for i := range dmn {
		spendings[i] = &models.Spending{
			AdId:       dmn[i].AdID,
			DateStart:  fmt.Sprintf("%v %v:00:00", dmn[i].DateStart, dmn[i].HourlyStat),
			HourlyStat: int32(dmn[i].HourlyStat),
			RawNumeric: dmn[i].RawNumeric,
			Cpm:        dmn[i].CPM,
			Cpc:        dmn[i].CPC,
			Ctr:        dmn[i].CTR,
			Clicks:     dmn[i].Clicks,
			Frequency:  dmn[i].Frequency,
			P25:        dmn[i].P25,
		}
	}
	return spendings
}
