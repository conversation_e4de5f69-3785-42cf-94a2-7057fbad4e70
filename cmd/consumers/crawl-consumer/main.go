package main

import (
	"time"

	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/consumers/crawl-consumer/handlers"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/internal/fbfetch"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

func main() {
	_ = app.LoadInitConfig() // just to trigger loading config from consul
	publisher, err := app.NewPublisher()
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to create publisher: ", err)
	}
	defer publisher.Close()

	marketClient := internal.CreateMarketClient(nil)
	fetcher := fbfetch.NewFetcher(fbfetch.FetcherConfig{
		RequestTimeout:      time.Duration(viper.GetInt("fetcher.request_timeout")) * time.Second,
		MaxIdleConnsPerHost: viper.GetInt("fetcher.max_idle_conns_per_host"),
	})
	crawlHandler := handlers.NewCrawlHandler(publisher, marketClient, fetcher)

	retryMiddleware := middleware.Retry{
		MaxRetries:          viper.GetInt("retry.max_retries"),
		InitialInterval:     time.Duration(viper.GetInt("retry.initial_interval")) * time.Minute,
		MaxInterval:         time.Duration(viper.GetInt("retry.max_interval")) * time.Minute,
		Multiplier:          viper.GetFloat64("retry.multiplier"),
		RandomizationFactor: viper.GetFloat64("retry.randomization_factor"),
	}

	err = app.StartNewConsumer(crawlHandler, retryMiddleware.Middleware)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to start consumer: ", err)
	}
}
