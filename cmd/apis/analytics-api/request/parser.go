package request

import (
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/internal/reports"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	"net/http"
	"strings"
	"time"
)

const (
	LayoutTime = "2006-01-02 15:04:05"
)

//type AnalyticsRequest struct {
//	CompanyID  int64   `json:"company_id"`
//	CountryID  int64   `json:"country_id"`
//	ProjectIDs []int64 `json:"project_ids,omitempty"`
//
//	DashboardType    string `json:"dashboard_type"`
//	DashboardSubType string `json:"dashboard_sub_type"`
//
//	StartTime time.Time `json:"start_time"`
//	EndTime   time.Time `json:"end_time"`
//	Timezone  string    `json:"timezone"`
//
//	FanpageIDs     []string `json:"fanpage_ids,omitempty"`
//	LandingPageIDs []string `json:"landing_page_ids,omitempty"`
//	MarketerIDs    []int64  `json:"marketer_ids,omitempty"`
//	ProductIDs     []int64  `json:"product_ids,omitempty"`
//	AdAccountIDs   []string `json:"adaccount_ids,omitempty"`
//	CampaignIDs    []string `json:"campaign_ids,omitempty"`
//	AdsetIDs       []string `json:"adset_ids,omitempty"`
//
//	GroupBy      []string `json:"group_by,omitempty"`
//	OrderBy      []string `json:"order_by,omitempty"`
//	Limit        uint64   `json:"limit,omitempty"`
//	Offset       uint64   `json:"offset,omitempty"`
//	TeamInCharge int64    `json:"team_in_charge"`
//}
//
//func (r *AnalyticsRequest) Validate() []string {
//	var errs []string
//	//if r.DashboardType == "" {
//	//	errs = append(errs, "dashboard_type is required")
//	//}
//
//	if r.StartTime.Unix() == 0 {
//		errs = append(errs, "start_time is required")
//	}
//
//	if r.EndTime.Unix() == 0 {
//		errs = append(errs, "end_time is required")
//	}
//
//	return errs
//}

func ParseAnalyticsRequest(httpReq *http.Request) (*services.AnalyticsRequest, error) {
	return parseQueryParams(httpReq)
}

// formatTimestampToDate converts a Unix timestamp string to the format "2006-01-02"
func formatTimestampToDate(timestampStr string) time.Time {
	timestamp := cast.ToInt64(timestampStr)
	t := time.Unix(timestamp, 0).UTC()
	return t
}

// parseQueryParams extracts parameters from a given URL string and populates the AnalyticsRequest struct.
func parseQueryParams(r *http.Request) (*services.AnalyticsRequest, error) {
	queryParams := r.URL.Query()

	// Convert timestamps to "YYYY-MM-DD" format
	startTime := formatTimestampToDate(queryParams.Get("start_time"))
	endTime := formatTimestampToDate(queryParams.Get("end_time"))

	orderByStr := queryParams.Get("order_by")
	groupByStr := queryParams.Get("group_by")

	orderBy := []string{"1 DESC"}
	groupBy := []string{"date"}

	if len(orderByStr) > 0 {
		orderBy = strings.Split(orderByStr, ",")
	}

	if len(groupByStr) > 0 {
		groupBy = strings.Split(groupByStr, ",")
	}

	return &services.AnalyticsRequest{
		ViewName:     queryParams.Get("dashboard_type"),
		GroupBy:      groupBy,
		OrderBy:      orderBy,
		Limit:        dbtool.GetQueryLimit(cast.ToUint64(queryParams.Get("limit")), 250, 1000),
		Offset:       cast.ToUint64(queryParams.Get("offset")),
		CompanyId:    cast.ToInt64(queryParams.Get("company_id")),
		CountryId:    cast.ToInt64(queryParams.Get("country_id")),
		StartTime:    startTime.Format(LayoutTime),
		EndTime:      endTime.Format(LayoutTime),
		TeamInCharge: cast.ToInt64(queryParams.Get("team_in_charge")),
		ProjectIds:   utils.ToInt64Slice(queryParams["project_ids"]),
		MarketerIds:  utils.ToInt64Slice(queryParams["marketer_ids"]),
		ProductIds:   utils.ToInt64Slice(queryParams["product_ids"]),
		CampaignIds:  cast.ToStringSlice(queryParams["campaign_ids"]),
		AdaccountIds: cast.ToStringSlice(queryParams["adaccount_ids"]),
		AdsetIds:     cast.ToStringSlice(queryParams["adset_ids"]),
		IsSummary:    groupBy[0] == reports.DimensionAll,
	}, nil

	//return &services.AnalyticsRequest{
	//
	//	CompanyID:        companyID,
	//	CountryID:        countryID,
	//	ProjectIDs:       utils.ToInt64Slice(queryParams["project_ids"]),
	//	DashboardType:    queryParams.Get("dashboard_type"),
	//	DashboardSubType: queryParams.Get("dashboard_sub_type"),
	//	StartTime:        startTime,
	//	EndTime:          endTime,
	//	FanpageIDs:       fanpageIDs,
	//	ProductIDs:       utils.ToInt64Slice(queryParams["product_ids"]),
	//	MarketerIDs:      utils.ToInt64Slice(queryParams["marketer_ids"]),
	//	CampaignIDs:      campaignIDs,
	//	AdsetIDs:         adsetIDs,
	//
	//	GroupBy:      groupBy,
	//	OrderBy:      orderBy,
	//	TeamInCharge: cast.ToInt64(queryParams.Get("team_in_charge")),
	//}, nil
}

//
//func buildFilter(req *AnalyticsRequest) []*models.Condition {
//	conds := make([]*models.Condition, 0)
//	if len(req.Timezone) == 0 {
//		req.Timezone = "Etc/UTC"
//	}
//
//	// default condition
//	conds = append(conds,
//		&models.Condition{
//			Expr: fmt.Sprintf("toDateTime(ts, '%v') >= ?", req.Timezone),
//			Args: utils.ToArgs(req.StartTime.Format(LayoutTime)),
//		},
//		&models.Condition{
//			Expr: fmt.Sprintf("toDateTime(ts, '%v') < ?", req.Timezone),
//			Args: utils.ToArgs(req.EndTime.Add(24 * time.Hour).Format(LayoutTime)),
//		},
//		&models.Condition{
//			Field:    "company_id",
//			Operator: models.Operator_EQ,
//			Args:     utils.ToArgs(req.CompanyID),
//		},
//	)
//
//	// primary condition
//	if len(req.ProjectIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "project_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.ProjectIDs),
//			},
//		)
//	}
//
//	if req.CountryID > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "country_id",
//				Operator: models.Operator_EQ,
//				Args:     utils.ToArgs(req.CountryID),
//			},
//		)
//	}
//
//	//additional condition
//	conds = applyAdditionalFilter(req, conds)
//	return conds
//}
//
//func applyAdditionalFilter(req *AnalyticsRequest, conds []*models.Condition) []*models.Condition {
//	if len(req.FanpageIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "fanpage_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.FanpageIDs),
//			},
//		)
//	}
//
//	if len(req.LandingPageIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "landing_page_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.LandingPageIDs),
//			},
//		)
//	}
//
//	if len(req.MarketerIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "marketer_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.MarketerIDs),
//			},
//		)
//	}
//
//	if len(req.ProductIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "product_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.ProductIDs),
//			},
//		)
//	}
//
//	if len(req.AdAccountIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "adaccount_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.AdAccountIDs),
//			},
//		)
//	}
//
//	if len(req.CampaignIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "campaign_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.CampaignIDs),
//			},
//		)
//	}
//
//	if len(req.AdsetIDs) > 0 {
//		conds = append(conds,
//			&models.Condition{
//				Field:    "adset_id",
//				Operator: models.Operator_IN,
//				Args:     utils.ToArgs(req.AdsetIDs),
//			},
//		)
//	}
//
//	return conds
//}

func ParseOrderDashboardRequest(httpReq *http.Request) (*services.OrderDashboardRequest, error) {
	return parseOrderDashboardQueryParams(httpReq)
}

func parseOrderDashboardQueryParams(r *http.Request) (*services.OrderDashboardRequest, error) {
	queryParams := r.URL.Query()

	// Convert timestamps to "YYYY-MM-DD" format
	startTime := formatTimestampToDate(queryParams.Get("start_time"))
	endTime := formatTimestampToDate(queryParams.Get("end_time"))

	orderByStr := queryParams.Get("order_by")
	groupByStr := queryParams.Get("group_by")
	// format: source_type1:source_id1|source_type2:source_id2|...
	sourcesStr := queryParams.Get("sources")

	didsStr := queryParams.Get("dids")

	var orderBy []string
	var groupBy []string
	var sources []string
	var dids []string

	if len(orderByStr) > 0 {
		orderBy = strings.Split(orderByStr, ",")
	}

	if len(groupByStr) > 0 {
		groupBy = strings.Split(groupByStr, ",")
	}

	if len(sourcesStr) > 0 {
		sources = strings.Split(sourcesStr, "|")
	}

	if len(didsStr) > 0 {
		dids = strings.Split(didsStr, ",")
	}

	return &services.OrderDashboardRequest{
		StartTime:     startTime.Format(LayoutTime),
		EndTime:       endTime.Format(LayoutTime),
		ProjectIds:    utils.ToInt64Slice(queryParams["project_ids"]),
		SaleReps:      utils.ToInt64Slice(queryParams["sale_reps"]),
		ProductIds:    utils.ToInt64Slice(queryParams["product_ids"]),
		MarketerIds:   utils.ToInt64Slice(queryParams["marketer_ids"]),
		Sources:       sources,
		CarriersCode:  queryParams["carriers_code"],
		TagIds:        utils.ToInt64Slice(queryParams["tag_ids"]),
		CompanyId:     cast.ToInt64(r.Header.Get("Companyid")),
		CountryId:     cast.ToInt64(r.Header.Get("Country-Ids")),
		GroupBy:       groupBy,
		OrderBy:       orderBy,
		Limit:         dbtool.GetQueryLimit(cast.ToUint64(queryParams.Get("limit")), 50, 250),
		Offset:        cast.ToUint64(queryParams.Get("offset")),
		DateRangeType: queryParams.Get("date_range_type"),
		IsViewDetail:  cast.ToBool(queryParams.Get("is_view_detail")),
		Action:        queryParams.Get("action"),
		SaleId:        utils.ToInt64Slice(queryParams["sale_id"]),
		CarePageId:    utils.ToInt64Slice(queryParams["care_page_id"]),
		TeamInCharge:  utils.ToInt64Slice(queryParams["team_in_charge"]),
		OrderStatus:   utils.ToInt64Slice(queryParams["order_status"]),
		Dids:          utils.ToInt64Slice(dids),
	}, nil
}
