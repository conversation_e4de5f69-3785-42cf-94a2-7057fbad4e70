package app

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"
	"time"

	"gitlab.com/a7923/athena-go/internal/middlewares"
	"gitlab.com/a7923/athena-go/internal/permissions"

	"github.com/justinas/alice"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/apis/market-api/handlers"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/micro/web"
	middleware "gitlab.com/a7923/athena-go/pkg/middlewares"
	"go-micro.dev/v5/client"
)

type Server struct {
	Name     string
	client   client.Client
	producer app.PublisherInterface
}

func NewServer(publisher app.PublisherInterface) *Server {
	return &Server{
		Name:     "market",
		producer: publisher,
	}
}

func (s *Server) SetGRPCClient(client client.Client) {
	s.client = client
}

func (s *Server) GetBasePath() string {
	return "/markets"
}

func (s *Server) GetName() string {
	return s.Name
}

func (s *Server) GetRoutes() web.Routes {
	marketClient := internal.CreateMarketClient(nil)
	crawlClient := internal.CreateCrawlClient(nil)
	userClient := internal.CreateNewUserServiceClient(nil)
	agsaleClient := internal.CreateAGSaleClient(nil)
	mdws := []alice.Constructor{}

	if viper.GetBool("logging.enable") {
		mdws = append(mdws, middleware.LoggingMiddleware)
	}

	fetcher := adcrawl.NewFetcher(adcrawl.FetcherConfig{
		RequestTimeout: time.Duration(viper.GetInt("market.fetch_request_time_out")) * time.Second,
	})

	legacyAuth := middlewares.NewLegacyAuthMiddleware()
	listLandingPageMdws := mdws
	return []web.Route{
		{
			Name:    "Create Analytic Account",
			Method:  http.MethodPost,
			Pattern: "/analytic-accounts",
			Handler: &handlers.CreateAnalyticAccountHandler{
				MarketClient: marketClient,
				Producer:     s.producer,
				CrawlClient:  crawlClient,
				Fetcher:      fetcher,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 30000, // 30 seconds
		},
		{
			Name:    "List Analytic Accounts",
			Method:  http.MethodGet,
			Pattern: "/analytic-accounts",
			Handler: &handlers.ListAnalyticAccountsHandler{
				MarketClient: marketClient,
				UserClient:   userClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 20000, // 20 seconds
		},
		{
			Name:    "Delete Analytic Account",
			Method:  http.MethodDelete,
			Pattern: "/analytic-accounts",
			Handler: &handlers.DeleteAnalyticAccountHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		{
			Name:    "List AdAccounts",
			Method:  http.MethodGet,
			Pattern: "/adaccounts",
			Handler: &handlers.ListAdAccountsHandler{
				MarketClient: marketClient,
				UserClient:   userClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 15000,
		},
		{
			Name:    "Update AdAccounts",
			Method:  http.MethodPut,
			Pattern: "/adaccounts",
			Handler: &handlers.UpdateAdAccountsHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:    "Delete AdAccounts",
			Method:  http.MethodDelete,
			Pattern: "/adaccounts",
			Handler: &handlers.DeleteAdAccountsHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:    "List Campaigns",
			Method:  http.MethodGet,
			Pattern: "/campaigns",
			Handler: &handlers.ListCampaignsHandler{
				MarketClient: marketClient,
				UserClient:   userClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 15000,
		},
		{
			Name:    "Update Campaigns",
			Method:  http.MethodPut,
			Pattern: "/campaigns/",
			Handler: &handlers.UpdateCampaignsHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:    "Update Campaign",
			Method:  http.MethodPut,
			Pattern: "/campaigns/assign-company",
			Handler: &handlers.AssignCompanyForCampaignsHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:    "Delete Campaigns",
			Method:  http.MethodDelete,
			Pattern: "/campaigns",
			Handler: &handlers.DeleteCampaignHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
				RequirePermissions: map[int64]int64{
					int64(permissions.Campaigns): permissions.CampaignsCreate,
				},
			},
			Timeout: 10000,
		},
		{
			Name:    "List Marketers",
			Method:  http.MethodGet,
			Pattern: "/marketers",
			Handler: &handlers.ListMarketersHandler{
				UserService: userClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:    "Get Analytic Account Token By AdID",
			Method:  http.MethodGet,
			Pattern: "/analytic-accounts/token",
			Handler: &handlers.GetAnalyticAccountTokenByAdIDHandler{
				MarketClient: marketClient,
			},
			Middlewares: mdws,
			Timeout:     10000,
		},
		{
			Name:    "Advanced Crawl",
			Method:  http.MethodPost,
			Pattern: "/crawl",
			Handler: &handlers.AdvancedCrawlHandler{
				CrawlClient: crawlClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000,
		},
		{
			Name:        "List Landing Pages",
			Method:      http.MethodGet,
			Pattern:     "/landings",
			Handler:     extractProjectIDsMiddleware(legacyAuth.Middleware(handlers.NewListLandingPagesHandler(agsaleClient))),
			Middlewares: listLandingPageMdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 15000,
		},
	}
}

// func extractProjectIDsMiddleware() alice.Constructor {
// 	return func(next http.Handler) http.Handler {
// 		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
// 			countryID := r.Header.Get("country_id")
// 			scopes := middlewares.ScopeResponse{}
// 			_ = json.Unmarshal([]byte(r.Header.Get("X-Gw-Scopes")), &scopes)
// 			if len(scopes.Data) > 0 {
// 				projects := extractProjectIdsFromScope(cast.ToInt(countryID), scopes.Data)
// 				r.Header.Set("project-ids", strings.Join(projects, ","))
// 			}

// 			next.ServeHTTP(w, r)
// 		})
// 	}
// }

func extractProjectIDsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		countryID := r.Header.Get("country_id")
		scopes := middlewares.ScopeResponse{}
		_ = json.Unmarshal([]byte(r.Header.Get("X-Gw-Scopes")), &scopes)
		if len(scopes.Data) > 0 {
			projects := extractProjectIdsFromScope(cast.ToInt(countryID), scopes.Data)
			r.Header.Set("project-ids", strings.Join(projects, ","))
		}
		next.ServeHTTP(w, r)
	})
}

func extractProjectIdsFromScope(countryId int, data [][]int) []string {
	log.Println("data: ", data)
	projectIdsFromScopes := make([]string, 0)
	m := make(map[int]bool)
	for _, v := range data {
		if len(v) < 1 {
			continue
		}
		if m[v[1]] {
			continue
		}

		if countryId > 0 && countryId != v[0] {
			continue
		}

		projectIdsFromScopes = append(projectIdsFromScopes, cast.ToString(v[1]))
		m[v[1]] = true
	}

	return projectIdsFromScopes
}
