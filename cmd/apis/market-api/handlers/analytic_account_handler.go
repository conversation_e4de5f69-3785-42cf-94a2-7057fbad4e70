package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gitlab.com/a7923/athena-go/internal/token"

	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/internal/adcrawl"
	"gitlab.com/a7923/athena-go/internal/fbfetch"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type CreateAnalyticAccountHandler struct {
	MarketClient services.MarketService
	Producer     app.PublisherInterface
	CrawlClient  services.CrawlService
	Fetcher      *adcrawl.Fetcher
}

type CreateAnalyticAccountRequest struct {
	Token string `json:"token"`
}

// Processes HTTP requests to create an analytic account and initiate a crawl operation.
func (h *CreateAnalyticAccountHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	createReq := &CreateAnalyticAccountRequest{}
	if err := json.NewDecoder(r.Body).Decode(&createReq); err != nil {
		logger.AthenaLogger.Errorw("Failed to decode request body", "error", err)
		errMsg := fmt.Sprintf("Failed to decode request body: %s", err)
		transhttp.RespondError(w, http.StatusBadRequest, errMsg)
		return
	}

	// Validate token by calling Facebook API
	isValid, err := h.Fetcher.ValidateToken(createReq.Token)
	if !isValid {
		logger.AthenaLogger.Infow("Invalid token", "error", err, "token", createReq.Token)
		invalidMsg := fmt.Sprintf("Token %s is invalid: %s", createReq.Token, err)
		transhttp.RespondError(w, http.StatusBadRequest, invalidMsg)
		return
	}

	// exchange token
	tokenResp, err := h.Fetcher.ExchangeToken(createReq.Token)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to exchange token", "error", err, "token", createReq.Token)
		errMsg := fmt.Sprintf("Failed to exchange token: %s", err)
		transhttp.RespondError(w, http.StatusBadRequest, errMsg)
		return
	}
	createReq.Token = tokenResp

	// Retrieve Company ID and Marketer ID and Markerter's name from header
	var companyID, marketerID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID is required")
		return
	}
	if companyID <= 0 {
		logger.AthenaLogger.Errorw("CompanyID must be greater than 0", "companyID", companyID)
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID must be greater than 0")
		return
	}

	if err := common.ExtractHeader(r, "X-Gw-Sub", &marketerID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, "Marketer ID is required")
		return
	}
	if marketerID <= 0 {
		logger.AthenaLogger.Errorw("Marketer ID must be greater than 0", "marketerID", marketerID)
		transhttp.RespondError(w, http.StatusBadRequest, "Marketer ID must be greater than 0")
		return
	}

	marketerName := r.Header.Get("X-Gw-Fullname")

	svcCreateReq := &services.CreateAnalyticAccountRequest{
		Token:        createReq.Token,
		CompanyId:    companyID,
		MarketerId:   marketerID,
		MarketerName: marketerName,
	}
	resp, err := h.MarketClient.CreateAnalyticAccount(r.Context(), svcCreateReq)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}

	if viper.GetBool("market.use_asynq_crawl") {
		crawlReq := &services.CrawlRequest{
			AccessToken:       createReq.Token,
			AnalyticAccountId: resp.AnalyticAccount.Id,
			MarketerId:        marketerID,
			CompanyId:         companyID,
		}
		_, err = h.CrawlClient.Crawl(r.Context(), crawlReq)
		if err != nil {
			logger.AthenaLogger.Errorw("Failed to crawl", "error", err, "token", createReq.Token)
			transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
			return
		}
	} else {
		fetchPayload := fbfetch.FetchObjectPayload{
			Type:        fbfetch.TypeFetchAdAccounts,
			URL:         "",
			AccessToken: createReq.Token,
			ExtraParams: map[string]interface{}{
				"AnalyticAccountID": cast.ToString(resp.AnalyticAccount.Id),
				"MarketerID":        cast.ToString(marketerID),
				"CompanyID":         cast.ToString(companyID),
			},
		}

		data, err := json.Marshal(fetchPayload)
		if err != nil {
			transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("failed to encode data in json format: %v", err))
			return
		}
		if err := h.Producer.PublishRoutingPersist(adcrawl.CrawlExchange, adcrawl.CrawlRoutingKey, data); err != nil {
			transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("failed to publish crawl adaccounts request: %v", err))
			return
		}
	}

	transhttp.RespondJSON(w, http.StatusOK, ToAnalyticAccountDTO(resp.AnalyticAccount))
}

// Default values for LitsAnalyticAccounts API
const (
	defaultAnalyticAccountPage      = 1
	defaultAnalyticAccountLimit     = 10
	defaultAnalyticAccountSortField = "createdAt"
	defaultAnalyticAccountSortOrder = "desc"
)

// Allowed values for LitsAnalyticAccounts API Query Parameters
var (
	mapAnalyticAccountSortFieldToDBField = map[string]string{
		"id":              "id",
		"username":        "username",
		"marketerName":    "marketer_name",
		"countAdAccounts": "count_ad_accounts",
		"createdAt":       "created_at",
	}
	allowedAnalyticAccountSortFields = []string{"id", "username", "marketerName", "countAdAccounts", "createdAt"}
	allowedAnalyticAccountStatus     = []string{"available", "unavailable"}
)

type ListAnalyticAccountsHandler struct {
	MarketClient services.MarketService
	UserClient   services.UserService
}

type ListAnalyticAccountsResponse struct {
	AnalyticAccountDTOs []AnalyticAccountDTO `json:"data"`
	Pagination          common.Pagination    `json:"pagination"`
}

func (h *ListAnalyticAccountsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	// Extract Pagination Parameters
	paginationParam, err := common.ExtractPaginationQueryParam(r, defaultAnalyticAccountPage, defaultAnalyticAccountLimit)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid pagination param: %v", err))
		return
	}

	// Extract Sorting Parameters
	sortByParam, err := common.ExtractSortByParam(r, defaultAnalyticAccountSortField, defaultAnalyticAccountSortOrder, allowedAnalyticAccountSortFields)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort by param: %v", err))
		return
	}

	// Extract Company ID from request header
	var companyID int64
	if err = common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "companyID must be greater than 0")
		return
	}

	svcReq := &services.ListAnalyticAccountsRequest{
		Page:      int32(paginationParam.Page),
		Limit:     int32(paginationParam.Limit),
		CompanyId: companyID,
		SortField: mapAnalyticAccountSortFieldToDBField[sortByParam.Field],
		IsAsc:     sortByParam.Order == "asc",
	}

	queryParams := r.URL.Query()

	// Search by name or ID (Optional)
	searchTerm := queryParams.Get("search")
	if searchTerm != "" {
		svcReq.SearchTerm = &searchTerm
	}

	// Filter by status (Optional)
	status := queryParams.Get("status")
	if status != "" {
		if !common.InSlice(status, allowedAnalyticAccountStatus) {
			transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid status, supported status: %+v", allowedAnalyticAccountStatus))
			return
		}
	}
	if status != "" {
		available := false
		if status == "available" {
			available = true
		}
		svcReq.IsAvailable = &available
	}

	// Filter by marketer ID (allowed multiple IDs) (Optional)
	queryMarketerIDs, ok := queryParams["addByID"]
	if ok {
		marketerIDs := make([]int64, len(queryMarketerIDs))
		for i := range queryMarketerIDs {
			marketerIDs[i] = cast.ToInt64(queryMarketerIDs[i])
		}
		svcReq.MarketerIds = marketerIDs
	}

	// Send request to Market GRPC service
	mids, err := token.GetMarketerIds(r, h.UserClient, svcReq.MarketerIds)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	svcReq.MarketerIds = mids

	resp, err := h.MarketClient.ListAnalyticAccounts(r.Context(), svcReq)
	if err != nil {
		logger.AthenaLogger.Errorw("failed to list analytic accounts", "error", err)
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	if resp.AnalyticAccounts == nil {
		resp.AnalyticAccounts = make([]*models.AnalyticAccount, 0)
	}

	// Prepare response
	analyticAccountDTOs := make([]AnalyticAccountDTO, len(resp.AnalyticAccounts))
	for i, analyticAccount := range resp.AnalyticAccounts {
		analyticAccountDTOs[i] = ToAnalyticAccountDTO(analyticAccount)
	}
	listResp := &ListAnalyticAccountsResponse{
		AnalyticAccountDTOs: analyticAccountDTOs,
		Pagination: common.Pagination{
			Page:  paginationParam.Page,
			Limit: paginationParam.Limit,
			Total: resp.Total,
		},
	}
	transhttp.RespondJSON(w, http.StatusOK, listResp)
}

type DeleteAnalyticAccountHandler struct {
	MarketClient services.MarketService
}

type BulkDeleteRequest struct {
	Ids []string `json:"ids"`
}

func (h *DeleteAnalyticAccountHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	// Decodes the bulk delete request body
	req := &BulkDeleteRequest{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}
	ids := make([]int64, len(req.Ids))
	for i, id := range req.Ids {
		ids[i] = cast.ToInt64(id)
	}

	// Extract Company ID from request header
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "companyID must be greater than 0")
		return
	}

	deleteReq := &services.DeleteAnalyticAccountRequest{
		CompanyId: companyID,
		Ids:       ids,
	}
	_, err := h.MarketClient.DeleteAnalyticAccount(r.Context(), deleteReq)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"message": fmt.Sprintf("Analytic account with these ids are deleted: %v", req.Ids),
	})
}

type AnalyticAccountDTO struct {
	ID         string `json:"id"`
	CompanyId  string `json:"company_id"`
	MarketerID string `json:"marketer_id"`
	Available  bool   `json:"available"`
	*models.AnalyticAccount
}

func ToAnalyticAccountDTO(analyticAccount *models.AnalyticAccount) AnalyticAccountDTO {
	return AnalyticAccountDTO{
		ID:              cast.ToString(analyticAccount.Id),
		CompanyId:       cast.ToString(analyticAccount.CompanyId),
		MarketerID:      cast.ToString(analyticAccount.MarketerId),
		Available:       analyticAccount.Available,
		AnalyticAccount: analyticAccount,
	}
}

type GetAnalyticAccountTokenByAdIDHandler struct {
	MarketClient services.MarketService
}

type GetAnalyticAccountTokenByAdIDResponse struct {
	Token string `json:"token"`
}

func (h *GetAnalyticAccountTokenByAdIDHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	params := r.URL.Query()
	adID := params.Get("adID")

	if adID == "" {
		transhttp.RespondError(w, http.StatusBadRequest, "Query parameter 'adID' is required")
		return
	}

	grpcResp, err := h.MarketClient.GetTokenByAdID(r.Context(), &services.GetTokenByAdIDRequest{
		AdId: cast.ToInt64(adID),
	})

	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("failed to get token by adID: %v", err))
		return
	}

	if grpcResp.Token == "" {
		transhttp.RespondError(w, http.StatusNotFound, fmt.Sprintf("token not found for adID: %s", adID))
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, GetAnalyticAccountTokenByAdIDResponse{
		Token: grpcResp.Token,
	})
}
