package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gitlab.com/a7923/athena-go/internal/token"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type ListCampaignsHandler struct {
	MarketClient services.MarketService
	UserClient   services.UserService
}

const (
	defaultCampaignPage      = 1
	defaultCampaignLimit     = 10
	defaultCampaignSortField = "updatedAt"
	defaultCampaignSortOrder = "asc"
)

var (
	mapCampaignSortFieldToDBField = map[string]string{
		"id":                  "id",
		"campaignName":        "name",
		"adAccountName":       "adaccount_name",
		"analyticAccountName": "analytic_account_name",
		"updatedAt":           "updated_at",
		"status":              "effective_status",
		"marketerID":          "marketer_id",
		"updatedByID":         "updated_by_id",
	}
	allowedCampaignSortFields = []string{"id", "campaignName", "adAccountName", "analyticAccountName",
		"updatedAt", "status", "marketerID", "updatedByID"}
	allowedCampaignStatus          = []string{"active", "paused", "deleted", "archived", "inProcess", "withIssues"}
	mapCampaignAPIStatusToDBStatus = map[string]string{
		"active":     "ACTIVE",
		"paused":     "PAUSED",
		"deleted":    "DELETED",
		"archived":   "ARCHIVED",
		"inProcess":  "IN_PROCESS",
		"withIssues": "WITH_ISSUES",
	}
)

type CampaignDTO struct {
	ID                string `json:"id"`
	AdaccountID       string `json:"adaccount_id"`
	MarkerterID       string `json:"marketer_id"`
	AnalyticAccountID string `json:"analytic_account_id"`
	UpdatedByID       string `json:"updated_by_id"`
	RunOnThisOrg      bool   `json:"run_on_this_org"`
	*models.Campaign
}

func ToCampaignDTO(campaign *models.Campaign, companyID int64) CampaignDTO {
	return CampaignDTO{
		ID:                cast.ToString(campaign.Id),
		AdaccountID:       cast.ToString(campaign.AdaccountId),
		MarkerterID:       cast.ToString(campaign.MarketerId),
		AnalyticAccountID: cast.ToString(campaign.AnalyticAccountId),
		UpdatedByID:       cast.ToString(campaign.UpdatedById),
		Campaign:          campaign,
		RunOnThisOrg:      campaign.Available,
	}
}

type ListCampaignsResponse struct {
	CampaignDTOs  []CampaignDTO     `json:"data"`
	CountNotInBiz int64             `json:"count_not_in_biz"`
	Pagination    common.Pagination `json:"pagination"`
}

func (h *ListCampaignsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	var userID int64
	if err := common.ExtractHeader(r, "X-Gw-Sub", &userID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract userID from request header: %v", err))
		return
	}

	// Extract Pagination Parameters
	paginationParam, err := common.ExtractPaginationQueryParam(r, defaultCampaignPage, defaultCampaignLimit)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid pagination param: %v", err))
		return
	}

	// Extract Sorting Parameters
	sortByParam, err := common.ExtractSortByParam(r, defaultCampaignSortField, defaultCampaignSortOrder, allowedCampaignSortFields)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort param: %v", err))
		return
	}

	// Retrieve companyID from request header
	companyIDStr := r.Header.Get("X-Gw-Companyid")
	if companyIDStr == "" {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID is required")
		return
	}
	companyID := cast.ToInt64(companyIDStr)

	listReq := &services.ListCampaignsRequest{
		Page:      int32(paginationParam.Page),
		Limit:     int32(paginationParam.Limit),
		CompanyId: companyID,
		SortField: mapCampaignSortFieldToDBField[sortByParam.Field],
		IsAsc:     sortByParam.Order == "asc",
	}

	// Search by name (Optional)
	queryParams := r.URL.Query()
	searchTerm := queryParams.Get("search")
	if searchTerm != "" {
		listReq.SearchTerm = &searchTerm
	}

	// Filter by status (Optional)
	status := queryParams.Get("status")
	if status != "" {
		if !common.InSlice(status, allowedCampaignStatus) {
			transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid status, supported status: %v", allowedCampaignStatus))
			return
		}
		status = mapCampaignAPIStatusToDBStatus[status]
		listReq.Status = &status
	}

	// Filter by analyticAccountID (allowed multiple IDs) (Optional)
	queryAnalyticAccountIDs, ok := queryParams["analyticAccountID"]
	if ok {
		analyticAccountIDs := make([]int64, len(queryAnalyticAccountIDs))
		for i := range queryAnalyticAccountIDs {
			analyticAccountIDs[i] = cast.ToInt64(queryAnalyticAccountIDs[i])
		}
		listReq.AnalyticAccountIds = analyticAccountIDs
	}

	// Filter by adAccountID (allowed multiple IDs) (Optional)
	queryAdaccountIDs, ok := queryParams["adAccountID"]
	if ok {
		adAccountIDs := make([]int64, len(queryAdaccountIDs))
		for i := range queryAdaccountIDs {
			adAccountIDs[i] = cast.ToInt64(queryAdaccountIDs[i])
		}
		listReq.AdaccountIds = adAccountIDs
	}

	// Filter by marketerID (allowed multiple IDs) (Optional)
	queryMarketerIDs, ok := queryParams["marketerID"]
	if ok {
		marketerIDs := make([]int64, len(queryMarketerIDs))
		for i := range queryMarketerIDs {
			marketerIDs[i] = cast.ToInt64(queryMarketerIDs[i])
		}
		listReq.MarketerIds = marketerIDs
	}

	// Filter by landingID (allowed multiple IDs) (Optional)
	queryLandingIDs, ok := queryParams["landingID"]
	if ok {
		var err error
		for i := range queryLandingIDs {
			if _, err = uuid.Parse(queryLandingIDs[i]); err != nil {
				transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid uuid format (landingID): %v", queryLandingIDs[i]))
				return
			}
		}
		listReq.LandingIds = queryLandingIDs
	}

	mids, err := token.GetMarketerIds(r, h.UserClient, listReq.MarketerIds)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to get marketer ids", "error", err)
		errMsg := fmt.Sprintf("Failed to get marketer ids: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	listReq.MarketerIds = mids

	// Send request to Market GRPC service
	listResp, err := h.MarketClient.ListCampaigns(r.Context(), listReq)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}

	// Prepare response
	campaignDTOs := make([]CampaignDTO, len(listResp.Campaigns))
	for i, campaign := range listResp.Campaigns {
		campaignDTOs[i] = ToCampaignDTO(campaign, companyID)
	}
	transhttp.RespondJSON(w, http.StatusOK, ListCampaignsResponse{
		CampaignDTOs:  campaignDTOs,
		CountNotInBiz: listResp.CountNotInBiz,
		Pagination: common.Pagination{
			Page:  paginationParam.Page,
			Limit: paginationParam.Limit,
			Total: listResp.Total,
		}})
}

type UpdateCampaignsHandler struct {
	MarketClient services.MarketService
}

type UpdateCampaignsRequest struct {
	CampaignIDs []string `json:"campaign_ids"`
	MarketerID  string   `json:"marketer_id"`
	LandingID   string   `json:"landing_id"`
	LandingName string   `json:"landing_name"`
}

type UpdateCampaignsResponse struct {
	UpdatedById string `json:"updated_by_id"`
	*services.BulkUpdateCampaignsResponse
}

func (h *UpdateCampaignsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	var req UpdateCampaignsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(req.CampaignIDs) == 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Campaign IDs are required")
		return
	}

	// Extract UpdatedByID from request header
	var updatedByID int64
	if err := common.ExtractHeader(r, "X-Gw-Sub", &updatedByID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract updatedByID from request header: %v", err))
		return
	}
	if updatedByID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Updated By ID must greater than 0")
		return
	}

	// Extract companyID from request header
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID must greater than 0")
		return
	}

	campaignIDs := make([]int64, len(req.CampaignIDs))
	for i, id := range req.CampaignIDs {
		campaignIDs[i] = cast.ToInt64(id)
	}
	updateReq := &services.BulkUpdateCampaignsRequest{
		CompanyId:   companyID,
		CampaignIds: campaignIDs,
		UpdatedById: cast.ToInt64(updatedByID),
	}
	if req.LandingID != "" && req.LandingName != "" {
		updateReq.LandingId = &req.LandingID
		updateReq.LandingName = &req.LandingName
	}
	if req.MarketerID != "" {
		marketerID, err := cast.ToInt64E(req.MarketerID)
		if err != nil {
			transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid marketer ID: %v", req.MarketerID))
			return
		}
		updateReq.MarketerId = &marketerID
	}

	resp, err := h.MarketClient.BulkUpdateCampaigns(r.Context(), updateReq)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	transhttp.RespondJSON(w, http.StatusOK, UpdateCampaignsResponse{
		UpdatedById:                 cast.ToString(updatedByID),
		BulkUpdateCampaignsResponse: resp,
	})
}

type DeleteCampaignHandler struct {
	MarketClient services.MarketService
}

type DeleteCampaignRequest struct {
	IDs []string `json:"ids"`
}

func (h *DeleteCampaignHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	var req DeleteCampaignRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.AthenaLogger.Errorw("Failed to decode request body", "error", err)
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}

	if len(req.IDs) == 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Ad account IDs are required")
		return
	}

	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract companyID from request header: %v", err))
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID must greater than 0")
		return
	}

	campaignIds := make([]int64, len(req.IDs))
	for i, id := range req.IDs {
		campaignIds[i] = cast.ToInt64(id)
	}

	_, err := h.MarketClient.BulkDeleteCampaigns(r.Context(), &services.BulkDeleteCampaignsRequest{
		Ids:       campaignIds,
		CompanyId: companyID,
	})
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to delete campaigns", "error", err, "campaignIds", campaignIds)
		errMsg := fmt.Sprintf("Failed to delete campaigns: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
	})
}

type AssignCompanyForCampaignsHandler struct {
	MarketClient services.MarketService
}

type AssignCompanyForCampaignsRequest struct {
	CampaignIDs []string `json:"campaign_ids"`
	Disable     bool     `json:"disable"`
}

type AssignCompanyForCampaignsCampaignResponse struct {
	*services.AssignCompanyForCampaignsResponse
}

func (h *AssignCompanyForCampaignsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	// Extract companyID from request header
	var companyID int64
	if err := common.ExtractHeader(r, "X-Gw-Companyid", &companyID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, err.Error())
		return
	}
	if companyID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Company ID must greater than 0")
		return
	}

	// Extract UpdatedByID from request header
	var updatedByID int64
	if err := common.ExtractHeader(r, "X-Gw-Sub", &updatedByID); err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to extract updatedByID from request header: %v", err))
		return
	}
	if updatedByID <= 0 {
		transhttp.RespondError(w, http.StatusBadRequest, "Updated By ID must greater than 0")
		return
	}

	var httpReq AssignCompanyForCampaignsRequest
	if err := json.NewDecoder(r.Body).Decode(&httpReq); err != nil {
		logger.AthenaLogger.Errorw("Failed to decode request body", "error", err)
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to decode request body: %v", err))
		return
	}

	campaignIDs := make([]int64, len(httpReq.CampaignIDs))
	for i, idStr := range httpReq.CampaignIDs {
		campaignID, err := cast.ToInt64E(idStr)
		if err != nil {
			transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid campaign ID: %v", idStr))
			return
		}
		campaignIDs[i] = campaignID
	}

	assignReq := &services.AssignCompanyForCampaignsRequest{
		CampaignIds: campaignIDs,
		UpdatedById: updatedByID,
	}
	assignReq.Disable = httpReq.Disable

	resp, err := h.MarketClient.AssignCompanyForCampaigns(r.Context(), assignReq)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to assign company for campaigns", "error", err, "campaignIDs", httpReq.CampaignIDs)
		errMsg := fmt.Sprintf("Failed to update campaign: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, errMsg)
		return
	}
	transhttp.RespondJSON(w, http.StatusOK, AssignCompanyForCampaignsCampaignResponse{
		AssignCompanyForCampaignsResponse: resp,
	})
}
