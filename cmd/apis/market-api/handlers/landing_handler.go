package handlers

import (
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gitlab.com/a7923/athena-go/internal/adcrawl/domain"
	"gitlab.com/a7923/athena-go/pkg/logger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type ListLandingPagesHandler struct {
	AGSaleClient services.AGSaleService
}

func NewListLandingPagesHandler(AGSaleClient services.AGSaleService) *ListLandingPagesHandler {
	return &ListLandingPagesHandler{
		AGSaleClient: AGSaleClient,
	}
}

type ListLandingPagesResponse struct {
	LandingPages []domain.LandingPage `json:"landing_pages"`
	Count        int                  `json:"count"`
}

func (h *ListLandingPagesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	defer func() {
		logger.AthenaLogger.Debugw("Processed request", "url", r.URL.Path, "took", time.Since(start).Milliseconds())
	}()

	queryParams := r.URL.Query()
	projectIDsValue := r.Header.Get("project-ids")
	projectIDStrings := strings.Split(projectIDsValue, ",")
	if len(projectIDStrings) == 0 {
		transhttp.RespondError(w, http.StatusNotFound, "There's no project assigned for this account")
		return
	}

	projectIDs := make([]int32, len(projectIDStrings))
	for i, projectIDString := range projectIDStrings {
		projectID, err := strconv.Atoi(projectIDString)
		if err != nil {
			transhttp.RespondError(w, http.StatusBadRequest, "Invalid project ID")
			return
		}
		projectIDs[i] = int32(projectID)
	}
	log.Printf("projectIDs: %v", projectIDs)

	grpcGetLandingPagesReq := &services.LandingPageRequest{
		GetAll:     true,
		ProjectIds: projectIDs,
	}
	search := queryParams.Get("search")
	if search != "" {
		grpcGetLandingPagesReq.Search = &search
	}
	grpcGetLandingPagesResp, err := h.AGSaleClient.GetLandingPages(r.Context(), grpcGetLandingPagesReq)
	if err != nil {
		logger.AthenaLogger.Errorf("Failed to get landing pages from agsale service: %v", err)
		transhttp.RespondError(w, http.StatusInternalServerError, "Failed to get landing pages")
		return
	}

	landingPages := make([]domain.LandingPage, len(grpcGetLandingPagesResp.LandingPages))
	for i := range grpcGetLandingPagesResp.LandingPages {
		landingPages[i] = domain.LandingPage{
			ID:   grpcGetLandingPagesResp.LandingPages[i].Id,
			Name: grpcGetLandingPagesResp.LandingPages[i].Name,
		}
	}
	response := ListLandingPagesResponse{
		LandingPages: landingPages,
		Count:        len(landingPages),
	}

	transhttp.RespondJSON(w, http.StatusOK, response)
}
