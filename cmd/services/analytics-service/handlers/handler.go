package handlers

import (
	"context"
	"errors"
	"github.com/Masterminds/squirrel"
	tables2 "gitlab.com/a7923/athena-go/cmd/services/analytics-service/tables"
	"gitlab.com/a7923/athena-go/internal/reports"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

type AnalyticsHandler struct {
	db *dbtool.ConnectionManager
}

func (e *AnalyticsHandler) QueryReport(ctx context.Context, req *services.AnalyticsRequest, response *services.AnalyticsResponse) error {
	handler := reports.GetViewHandler(req)
	if handler == nil {
		return errors.New("view not found")
	}

	qb, err := handler.GetQuery(req)
	if err != nil {
		return err
	}

	sqlTool := dbtool.NewSelect(ctx, e.db.GetConnection(), tables2.GetTable(req.ViewName), &models.Row{})
	query, args, err := qb.ToSql()
	if err != nil {
		return err
	}

	results := make([]*models.Row, 0)
	rows, err := sqlTool.ExecQuery(query, args...)
	if err != nil {
		return err
	}

	for rows.Next() {
		row := make(map[string]interface{})
		if err := rows.MapScan(row); err != nil {
			return err
		}

		protoRow, err := handler.ProcessRow(row, req)
		if err != nil {
			return err
		}
		results = append(results, protoRow)
	}
	response.Data = results
	return nil
}

func (e *AnalyticsHandler) Query(ctx context.Context, req *services.AnalyticsRequest, response *services.AnalyticsResponse) error {
	qb := squirrel.Select(req.Fields...).From(req.ViewName)
	qb = reports.BuildClause(qb, req)
	sqlTool := dbtool.NewSelect(ctx, e.db.GetConnection(), tables2.GetTable(req.ViewName), &models.Row{})
	query, args, err := qb.ToSql()
	if err != nil {
		return err
	}

	results := make([]*models.Row, 0)
	rows, err := sqlTool.ExecQuery(query, args...)
	if err != nil {
		return err
	}

	for rows.Next() {
		row := make(map[string]interface{})
		if err := rows.MapScan(row); err != nil {
			return err
		}
		protoRow := &models.Row{Values: utils.ToJSONByte(row)}
		results = append(results, protoRow)
	}
	response.Data = results
	return nil
}

func NewAnalyticsHandler(db *dbtool.ConnectionManager) *AnalyticsHandler {
	return &AnalyticsHandler{db: db}
}

func (e *AnalyticsHandler) buildWhereClause(qb squirrel.SelectBuilder, conditions []*models.Condition) squirrel.SelectBuilder {
	for _, condition := range conditions {
		if len(condition.Expr) > 0 {
			args := utils.GetValueFromArgs(condition.Args)
			qb = qb.Where(condition.Expr, args)
			continue
		}

		if len(condition.Field) == 0 {
			continue
		}

		var pred interface{}
		args := utils.GetValueFromArgs(condition.Args)
		switch condition.Operator {
		case models.Operator_EQ:
			pred = squirrel.Eq{condition.Field: args}
		case models.Operator_NEQ:
			pred = squirrel.NotEq{condition.Field: args}
		case models.Operator_GT:
			pred = squirrel.Gt{condition.Field: args}
		case models.Operator_GTE:
			pred = squirrel.GtOrEq{condition.Field: args}
		case models.Operator_LT:
			pred = squirrel.Lt{condition.Field: args}
		case models.Operator_LTE:
			pred = squirrel.LtOrEq{condition.Field: args}
		case models.Operator_IN:
			pred = squirrel.Eq{condition.Field: args}
		case models.Operator_NOT_IN:
			pred = squirrel.NotEq{condition.Field: args}
		case models.Operator_LIKE:
			pred = squirrel.Like{condition.Field: args}
		case models.Operator_NOT_LIKE:
			pred = squirrel.NotLike{condition.Field: args}
		case models.Operator_IS_NULL:
			pred = squirrel.Eq{condition.Field: nil}
		case models.Operator_IS_NOT_NULL:
			pred = squirrel.NotEq{condition.Field: nil}
		case models.Operator_BETWEEN:
			//if len(args) >= 2 {
			//	pred = squirrel.And{
			//		squirrel.GtOrEq{condition.Field: args[0]},
			//		squirrel.LtOrEq{condition.Field: args[1]},
			//	}
			//}
		}
		qb = qb.Where(pred)
	}

	return qb
}

func (e *AnalyticsHandler) QueryOrderDashboardOverview(ctx context.Context, req *services.OrderDashboardRequest, response *services.AnalyticsResponse) error {
	handler := reports.GetOrderDashboardViewHandler(req)
	if handler == nil {
		return errors.New("view not found")
	}
	qb, err := handler.GetQuery(req)
	if err != nil {
		return err
	}
	sqlTool := dbtool.NewSelect(ctx, e.db.GetConnection(), tables2.GetTable("base_orders_dashboard"), &models.Row{})
	query, args, err := qb.ToSql()
	if err != nil {
		return err
	}
	results := make([]*models.Row, 0)
	rows, err := sqlTool.ExecQuery(query, args...)
	if err != nil {
		return err
	}

	for rows.Next() {
		row := make(map[string]interface{})
		if err := rows.MapScan(row); err != nil {
			return err
		}

		protoRow, err := handler.ProcessRow(row, req)
		if err != nil {
			return err
		}
		results = append(results, protoRow)
	}
	response.Data = results
	return nil
}
