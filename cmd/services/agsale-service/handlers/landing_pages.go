package handlers

import (
	"context"
	"github.com/Masterminds/squirrel"
	"gitlab.com/a7923/athena-go/cmd/services/agsale-service/tables"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func (a *AGSaleHandler) GetLandingPages(ctx context.Context, request *services.LandingPageRequest, response *services.LandingPageResponse) error {
	sqlTool := dbtool.NewSelect(ctx, a.db.GetConnection(), tables.GetLandingPagesTable(), &models.LandingPage{})
	limit := dbtool.GetQueryLimit(request.Query.GetLimit(), 50, 250)
	offset := dbtool.GetQueryOffset(limit, request.Query.GetPage())

	qb := squirrel.
		Select(sqlTool.GetQueryColumnList("")...).
		From(sqlTool.GetTable("")).Limit(limit).Offset(offset)

	if len(request.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id::text": request.Ids})
	} else if len(request.Id) > 0 {
		qb = qb.Where(squirrel.Eq{"id::text": request.Id})
	} else {
		// must be non-empty condition here
	}

	response.LandingPages = make([]*models.LandingPage, 0)
	err := sqlTool.Select(ctx, &response.LandingPages, qb)
	if err != nil {
		logger.AthenaLogger.Errorw("Failed to scan row", "error", err)
		return err
	}
	return nil
}
